{"name": "clientapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next dev", "start": "next start", "check": "tsc -b --noEmit .", "dev:zero-cache": "zero-cache-dev -p src/zero/schema.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@preact/signals-react": "^3.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@tanstack/react-query": "^5.66.0", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "exceljs": "^4.4.0", "jose": "^6.0.11", "js-cookie": "^3.0.5", "lucide-react": "^0.474.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.1", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.3", "@rocicorp/zero": "^0.19.2025050203"}, "devDependencies": {"@passionfroot/prisma-generator-zero": "^0.3.3", "@eslint/js": "^9.19.0", "@playwright/test": "^1.51.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.0.5", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "jsdom": "^26.0.0", "msw": "^2.7.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prisma": "^6.8.1", "tailwindcss": "^4", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vitest": "^3.0.5"}}