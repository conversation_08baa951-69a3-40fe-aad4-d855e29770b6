import React from "react";
import { Table } from "@tanstack/react-table";
import { Input } from "./input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "./select";

interface DataTableSearchProps<TData> {
    table: Table<TData>;
    searchableColumns: string[];
}

export function DataTableSearch<TData>({
    table,
    searchableColumns = [],
}: DataTableSearchProps<TData>) {
    const [searchColumn, setSearchColumn] = React.useState<string>(
        searchableColumns.length > 0 ? searchableColumns[0] : "name",
    );

    return (
        <div className="flex items-center gap-2">
            <Select value={searchColumn} onValueChange={setSearchColumn}>
                <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select column" />
                </SelectTrigger>
                <SelectContent>
                    {searchableColumns.length > 0
                        ? searchableColumns.map((column) => (
                              <SelectItem key={column} value={column}>
                                  {column.charAt(0).toUpperCase() +
                                      column.slice(1)}
                              </SelectItem>
                          ))
                        : table
                              .getAllColumns()
                              .filter((column) => typeof column.id === "string")
                              .map((column) => (
                                  <SelectItem key={column.id} value={column.id}>
                                      {column.id.charAt(0).toUpperCase() +
                                          column.id.slice(1)}
                                  </SelectItem>
                              ))}
                </SelectContent>
            </Select>
            <Input
                placeholder={`Filter by ${searchColumn}...`}
                value={
                    (table
                        .getColumn(searchColumn)
                        ?.getFilterValue() as string) || ""
                }
                onChange={(event) =>
                    table
                        .getColumn(searchColumn)
                        ?.setFilterValue(event.target.value)
                }
                className="max-w-sm"
            />
        </div>
    );
}
