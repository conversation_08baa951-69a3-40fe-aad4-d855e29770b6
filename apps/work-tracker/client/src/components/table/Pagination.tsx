import { Table } from "@tanstack/react-table";
import {
    Chevron<PERSON>eft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
} from "lucide-react";

const Pagination = <T,>({ table }: { table: Table<T> }) => {
    return (
        <div className="flex items-center gap-2">
            <button
                className="border rounded p-1 disabled:bg-stone-200"
                onClick={() => table.firstPage()}
                disabled={!table.getCanPreviousPage()}
            >
                <ChevronsLeft />
            </button>
            <button
                className="border rounded p-1 disabled:bg-stone-200"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
            >
                <ChevronLeft />
            </button>
            <button
                className="border rounded p-1 disabled:bg-stone-200"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
            >
                <ChevronRight />
            </button>
            <button
                className="border rounded p-1 disabled:bg-stone-200"
                onClick={() => table.lastPage()}
                disabled={!table.getCanNextPage()}
            >
                <ChevronsRight />
            </button>
            <span className="flex items-center gap-1">
                {`Page ${table.getState().pagination.pageIndex + 1} of ${table.getPageCount().toLocaleString()}`}
            </span>
        </div>
    );
};

export default Pagination;
