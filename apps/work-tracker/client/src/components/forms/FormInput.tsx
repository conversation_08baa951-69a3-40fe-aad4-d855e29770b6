import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DefaultInputProps } from "./types";

interface TextInputProps extends DefaultInputProps {
    value: string | number;
    type: "text" | "number";
    placeholder?: string;
}
const FormInput = (props: TextInputProps) => {
    const {
        label,
        property,
        type,
        value,
        onChange,
        required,
        error,
        placeholder,
    } = props;

    return (
        <div>
            <Label htmlFor={property} className={`${error && "text-red-700"}`}>
                {label} {required && <span className="text-red-500">*</span>}
            </Label>
            <Input
                id={property}
                name={property}
                onChange={(e) => onChange(e.target.value)}
                type={type}
                value={value as string | number}
                placeholder={placeholder}
                className={
                    error && "bg-red-50 text-red-700 border border-red-200"
                }
            />
            {error && <Label className="text-red-700">{error}</Label>}
        </div>
    );
};

export default FormInput;
