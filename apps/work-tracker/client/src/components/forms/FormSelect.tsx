import { useMemo } from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../ui/select";
import { DefaultInputProps } from "./types";
import { Label } from "../ui/label";

interface SelectInputProps extends DefaultInputProps {
    value: string | boolean;
    options: { label: string; value: string | number | boolean }[];
}

const FormSelect = (props: SelectInputProps) => {
    const { label, property, value, onChange, required, error } = props;
    // Create mappings for select options if type is select
    // Originally created to handle duplicate value/display options
    const { indexToValue, valueToIndex, processedOptions } = useMemo(() => {
        const indexToValue = new Map<string, string | number | boolean>();
        const valueToIndex = new Map<string, string>();

        const processed = props.options.map((option, index) => {
            const indexKey = `${property}-${index}`;
            const stringValue = String(option.value);

            indexToValue.set(indexKey, option.value);
            valueToIndex.set(stringValue, indexKey);

            return {
                ...option,
                indexKey,
            };
        });

        return {
            indexToValue,
            valueToIndex,
            processedOptions: processed,
        };
    }, [props.options, property]);

    // Convert current value to index key
    const currentIndexKey = valueToIndex.get(String(value)) || "";

    return (
        <div>
            <Label htmlFor={property} className={`${error && "text-red-700"}`}>
                {label} {required && <span className="text-red-500">*</span>}
            </Label>
            <Select
                onValueChange={(indexKey) => {
                    const originalValue = indexToValue.get(indexKey);
                    onChange(
                        originalValue === "true"
                            ? true
                            : originalValue === "false"
                              ? false
                              : (originalValue as string | number),
                    );
                }}
                value={currentIndexKey}
            >
                <SelectTrigger
                    id={property}
                    className={
                        error && "bg-red-50 text-red-700 border border-red-200"
                    }
                >
                    <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                    {processedOptions.map((option) => (
                        <SelectItem
                            key={option.indexKey}
                            value={option.indexKey}
                        >
                            {option.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            {error && <Label className="text-red-700">{error}</Label>}
        </div>
    );
};

export default FormSelect;
