import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../ui/select";
import useEmployeePunchStore from "@/stores/EmployeePunchStore";
import { Button } from "../ui/button";
import {
    <PERSON>,
    CardContent,
    <PERSON>Footer,
    <PERSON>Header,
    CardTitle,
} from "../ui/card";
import { Label } from "../ui/label";
import { useEffect, useState } from "react";
import { EmployeeLowercase } from "@/types/DashboardModels";
import {
    AddEmployeeToJob,
    GetEmployeesNotPunchedIntoJob,
    JobPunch,
} from "@/features/devices/api";
import useMachineDetailStore from "@/stores/MachineDetailStore";
import { toast } from "sonner";
import { getAllJobs } from "@/features/jobs/api";
import { Job } from "@/types";

const AddEmployeePunch = () => {
    const { machineDetail, triggerMachineDetailRefresh } =
        useMachineDetailStore();
    const [employeeList, setEmployeeList] = useState<EmployeeLowercase[]>([]);
    const [jobList, setJobList] = useState<Job[]>([]);
    const [employeeId, setEmployeeId] = useState<string>();
    const [jobId, setJobId] = useState<string>();
    const { isDialogOpen, closeDialog, operatorType } = useEmployeePunchStore();

    const handlePunchIn = async () => {
        let response;

        closeDialog();

        if (operatorType == "Primary") {
            if (!jobId || !employeeId) {
                toast.error("No Job Selected", {
                    description: "Please select a job",
                });
                return;
            }

            response = await JobPunch({
                EmployeeId: employeeId,
                JobId: jobId,
                DeviceKey: machineDetail!.EmpJobDetail.DeviceKey,
                OperatorType: "Primary",
            });
        } else {
            if (!machineDetail?.EmpJobDetail.JobId || !employeeId) {
                toast.error("No Job Selected", {
                    description: "Please select a job",
                });
                return;
            }

            response = await AddEmployeeToJob({
                EmployeeId: employeeId,
                JobId: machineDetail?.EmpJobDetail.JobId,
                DeviceKey: machineDetail.EmpJobDetail.DeviceKey,
                OperatorType: operatorType,
            });
        }

        if (response.status === "success") {
            toast("Success", { description: "Employee Punched In" });
        } else {
            toast.error("Error", {
                description: "An error occured",
            });
        }

        triggerMachineDetailRefresh();
    };

    useEffect(() => {
        const getEmployees = async () => {
            if (operatorType == "Primary") {
                const response = await GetEmployeesNotPunchedIntoJob({
                    EmployeeType: 1,
                    DeviceKey: "",
                    JobId: "",
                });
                if (response.status === "success") {
                    setEmployeeList(response.payload);
                }
                return;
            }

            if (!machineDetail?.EmpJobDetail.JobId) return;

            const EmployeeType = operatorType === "Secondary" ? 1 : 2;
            const response = await GetEmployeesNotPunchedIntoJob({
                EmployeeType: EmployeeType,
                DeviceKey: machineDetail.EmpJobDetail.DeviceKey,
                JobId: machineDetail?.EmpJobDetail.JobId,
            });
            if (response.status === "success") {
                setEmployeeList(response.payload);
            }
        };

        const getJobs = async () => {
            const response = await getAllJobs();
            if (response) {
                setJobList(response);
            }
        };

        getEmployees();
        if (operatorType == "Primary") getJobs();
    }, [
        operatorType,
        machineDetail?.EmpJobDetail.DeviceKey,
        machineDetail?.EmpJobDetail.JobId,
    ]);

    if (!isDialogOpen) return null;

    return (
        <Card
            className="fixed inset-0 bg-black/30 z-50 flex items-center justify-center"
            onClick={() => closeDialog()}
        >
            <div
                className="sm:max-w-[425px] w-full p-4 bg-white rounded-lg shadow-lg z-60"
                onClick={(e) => e.stopPropagation()}
            >
                <CardHeader>
                    <CardTitle className="text-xl font-semibold mb-4">
                        Add new {operatorType}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Label>Employee Id</Label>
                    <Select
                        value={employeeId}
                        onValueChange={(value) => setEmployeeId(value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="-- Select an option --" />
                        </SelectTrigger>
                        <SelectContent>
                            {employeeList.map((employee: EmployeeLowercase) => (
                                <SelectItem
                                    key={employee.employeeId}
                                    value={employee.employeeId}
                                >
                                    {employee.employeeId}: {employee.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    {operatorType == "Primary" ? (
                        <>
                            <Label>Job Id</Label>
                            <Select
                                value={jobId}
                                onValueChange={(value) => setJobId(value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="-- Select an option --" />
                                </SelectTrigger>
                                <SelectContent>
                                    {jobList.map((job: Job) => (
                                        <SelectItem
                                            key={job.jobId}
                                            value={job.jobId}
                                        >
                                            {job.jobId}: {job.deviceKey}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </>
                    ) : null}
                </CardContent>
                <CardFooter className="justify-between flex mt-4">
                    <Button variant={"destructive"} onClick={closeDialog}>
                        Close
                    </Button>
                    <Button variant={"default"} onClick={handlePunchIn}>
                        Add
                    </Button>
                </CardFooter>
            </div>
        </Card>
    );
};

export default AddEmployeePunch;
