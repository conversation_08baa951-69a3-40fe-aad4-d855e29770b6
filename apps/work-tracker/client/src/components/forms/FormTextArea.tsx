import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";

interface FormTextAreaProps {
    label: string;
    property: string;
    placeholder: string;
    error: string;
    initialData?: string;
}

const FormTextArea = ({
    label,
    property,
    placeholder,
    error,
    initialData,
}: FormTextAreaProps) => {
    return (
        <>
            <Label htmlFor={property}>{label}</Label>
            <Textarea
                id={property}
                name={property}
                placeholder={placeholder}
                defaultValue={initialData ?? ""}
            />
            {error && <Label className="text-red-700">{error}</Label>}
        </>
    );
};

export default FormTextArea;
