import { ExclamationTriangleIcon } from "@radix-ui/react-icons";

interface ErrorMessageProps {
    message: string
}

export function ErrorMessage({ message }: ErrorMessageProps) {
    return (
        <div className="flex items-center gap-2 p-3 text-sm rounded-md bg-red-50 text-red-700 border border-red-200">
            <ExclamationTriangleIcon className="h-4 w-4 flex-shrink-0" />
            <p>{message}</p>
        </div>
    );
}

