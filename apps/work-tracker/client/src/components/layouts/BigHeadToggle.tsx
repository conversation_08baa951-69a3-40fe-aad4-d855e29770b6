import { useBigHeadModeStore } from "@/stores/BigHeadModeStore";
import { Button } from "../ui/button";
import { HardHat } from "lucide-react";

const BigHeadToggle = () => {
    const { isBigHeadMode, toggleBigHead } = useBigHeadModeStore();

    return (
        <Button
            variant={!isBigHeadMode ? "outline" : "default"}
            className="flex items-center gap-2"
            onClick={toggleBigHead}
            size={isBigHeadMode ? "lg" : "default"}
        >
            <HardHat className="size-10" />
            Big Head Mode {isBigHeadMode ? "ON" : "OFF"}
        </Button>
    );
};

export default BigHeadToggle;
