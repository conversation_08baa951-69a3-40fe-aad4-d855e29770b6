import { Card, CardContent } from "@/components/ui/card";
import { User, Activity, Cpu } from "lucide-react";
import { useRouter } from "next/navigation";

type HomeCardProps = {
    title: string;
};

const renderIcon = (title: string) => {
    switch (title) {
        case "Devices":
            return <Cpu />;
        case "Jobs":
            return <Activity />;
        case "Parts":
            return <Cpu />;
        case "Employees":
            return <User />;
    }
};

const HomeCard = ({ title }: HomeCardProps) => {
    const router = useRouter();

    const navTo = (title: string) => {
        switch (title) {
            case "Devices":
                router.push("/device");
                break;
            case "Jobs":
                router.push("/jobs");
                break;
            case "Parts":
                router.push("/parts");
                break;
            case "Employees":
                router.push("/employees");
                break;
            default:
                router.push("/");
        }
    };

    return (
        <Card
            className="w-full h-48 sm:h-56 lg:h-60 flex items-center justify-center shadow-md rounded-xl cursor-pointer hover:shadow-lg transition-shadow duration-200"
            onClick={() => navTo(title)}
        >
            <CardContent className="flex flex-col items-center justify-center p-4 gap-3">
                <div className="text-4xl sm:text-5xl text-primary">
                    {renderIcon(title)}
                </div>
                <span className="text-lg sm:text-xl font-medium text-center">
                    {title}
                </span>
            </CardContent>
        </Card>
    );
};

export default HomeCard;
