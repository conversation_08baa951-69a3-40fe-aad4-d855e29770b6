'use client'
import R from 'react'
import Navbar from '@/components/nav/Navbar'
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import ZeroProviderClient from "@/zero/ZeroProvider";
import { Toaster } from "sonner";

const queryClient = new QueryClient();

export default function({ children }: <PERSON>.<PERSON>psWithChildren<{}>) {
    return <QueryClientProvider client={queryClient}>
        <ZeroProviderClient>
            <Toaster/>
            <Navbar/>
            {children}
        </ZeroProviderClient>
    </QueryClientProvider>
}
