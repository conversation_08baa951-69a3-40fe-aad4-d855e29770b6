'use client'
import AddEmployeePunch from "@/components/forms/AddEmployeePunch";
import PrimaryEmployeePunch from "@/components/machineDetail/PrimaryEmployeePunch";
import OtherEmployeePunch from "@/components/machineDetail/OtherEmployeePunch";
import OtherTechnicianPunch from "@/components/machineDetail/OtherTechnicianPunch";
import { GetMachineDetail, GetVorneJob } from "@/features/devices/api";
import useTitle from "@/hooks/useTitle";
import useMachineDetailStore from "@/stores/MachineDetailStore";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import PageLoading from "@/components/loading/PageLoading";
import { Button } from "@/components/ui/button";
import JobDetailCard from "@/components/machineDetail/JobDetailCard";
import { MachineDetailViewModel } from "@/types/MachineDetailModels";
import BigHeadToggle from "@/components/layouts/BigHeadToggle";
import useEmployeePunchStore from "@/stores/EmployeePunchStore";
import ReasonStateCard from "@/components/machineDetail/ReasonStateCard";

const MachineDetailPage = ({ machineId }: { machineId: string }) => {
    useTitle("Machine Details");
    const {
        machineDetail,
        setMachineDetail,
        refreshMachine,
        triggerMachineDetailRefresh,
        setVorneJob,
    } = useMachineDetailStore();
    const { openDialog } = useEmployeePunchStore();

    const [loading, setLoading] = useState(true);

    const hasLeftPage = () => {
        if (machineDetail?.EmpJobDetail.DeviceKey) {
            if (
                !(
                    `/device/MachineDetail/${machineDetail.EmpJobDetail.DeviceKey}` ==
                    window.location.pathname
                )
            )
                return true;
        }
        return false;
    };

    const verifyJob = (job: string, machineDetail: MachineDetailViewModel) => {
        if (job === null || job === undefined || job === "unknown") job = "";
        if (job !== machineDetail?.EmpJobDetail.JobId) {
            if (hasLeftPage()) {
                return;
            } else {
                openDialog("Primary");
                toast.error("Error", {
                    description:
                        "Vorne job does not match SFIS job. Please login to SFIS job.",
                });
            }
        }
    };

    const fetchMachine = async () => {
        if (!machineId) return;
        const response = await GetMachineDetail(machineId);
        if (response.status === "success") {
            setMachineDetail(response.payload);
            const job = await fetchVorneJob(response.payload);
            verifyJob(job, response.payload); // check for matching id with sfis
            setVorneJob(job);
        } else {
            if (hasLeftPage()) return;
            toast.error("An error occured", {
                description: "Error: " + response.message,
                action: (
                    <Button onClick={triggerMachineDetailRefresh}>Retry</Button>
                ),
            });
        }
        setLoading(false);
    };

    const fetchVorneJob = async (machineDetail: MachineDetailViewModel) => {
        if (!machineDetail?.MachineVorneDetails?.DeviceDetail?.IpAddress)
            return "";
        const response = await GetVorneJob(
            machineDetail.MachineVorneDetails.DeviceDetail.IpAddress,
        );
        if (response.status === "success") {
            const job = response.payload.data.events[0][0];
            if (job === "null") return "";
            return job;
        } else {
            if (hasLeftPage()) return;
            toast.error("An error occured", {
                description: "Error fetching board job.",
                action: (
                    <Button onClick={triggerMachineDetailRefresh}>Retry</Button>
                ),
            });
        }
    };

    useEffect(() => {
        fetchMachine();
        const interval = setInterval(() => {
            triggerMachineDetailRefresh();
        }, 5000);
        return () => clearInterval(interval);
    }, [refreshMachine]);

    return (
        <main className="w-full max-w-screen-xl p-4 mx-auto flex flex-col gap-8">
            {loading ? (
                <PageLoading />
            ) : (
                <>
                    <BigHeadToggle />
                    <ReasonStateCard />
                    <AddEmployeePunch />
                    <PrimaryEmployeePunch />
                    <JobDetailCard />
                    <div className="flex justify-center items-center gap-4">
                        <div className="grid md:grid-cols-2 md:grid-rows-1 grid-cols-1 grid-rows-2 gap-8 w-full">
                            <OtherEmployeePunch />
                            <OtherTechnicianPunch />
                        </div>
                    </div>
                </>
            )}
        </main>
    );
};

export default MachineDetailPage;
