import { Separator } from "@radix-ui/react-separator";
import { Card, CardContent } from "../ui/card";

import useMachineDetailStore from "@/stores/MachineDetailStore";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../ui/select";
import { useEffect, useState } from "react";
import { useBigHeadModeStore } from "@/stores/BigHeadModeStore";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
} from "../ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";
import {
    Drawer,
    DrawerContent,
    DrawerHeader,
    DrawerTitle,
    DrawerTrigger,
} from "../ui/drawer";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Label } from "../ui/label";
import {
    AddMissingDownReason,
    GetMissingDeviceDownReasons,
} from "@/features/devices/api";
import { MissingDownReason } from "@/types/MachineDetailModels";
import { signal } from "@preact/signals-react";
import { toast } from "sonner";

const JobDetailCard = () => {
    const [selectedDownReason, setSelectedDownReason] = useState<string>("");
    const [isJobCountOpen, setIsJobCountOpen] = useState<boolean>(true);
    const [isDownDrawerOpen, setIsDownDrawerOpen] = useState<boolean>(false);


    const missingReasonSelect = signal<string | undefined>();
    const {
        machineDetail,
        vorneJob,
        triggerMachineDetailRefresh,
        setMachineDetail,
    } = useMachineDetailStore();
    const [deviceDownReasons, setDeviceDownReasons] = useState<
        MissingDownReason[]
    >([]);

    const fetchReasons = async () => {
        if (!machineDetail?.MachineVorneDetails.DeviceDetail?.DeviceKey) return;
        const response = await GetMissingDeviceDownReasons(
            machineDetail?.MachineVorneDetails.DeviceDetail?.DeviceKey,
        );
        if (response.status === "success")
            setDeviceDownReasons(response.payload);
        };

    useEffect(() => {
        fetchReasons();
    }, []);

    const addMissingDownReason = async (value: string) => {
        missingReasonSelect.value = value;
        if (!value) {
            toast.error("Please select a reason");
            return;
        }

        if (machineDetail === null) {
            toast.error("Not signed into job");
            return;
        }

        const ipAddress =
            machineDetail.MachineVorneDetails.DeviceDetail?.IpAddress;
        if (!ipAddress) {
            alert("Comment creation failed. No IP Address found.");
            return;
        }

        try {
            // Optimistic update
            machineDetail.MachineVorneDetails.Last_Down_Time_Info.Process_state_reason_display_name =
                value;
            setMachineDetail(machineDetail);
            const response = await AddMissingDownReason(value, ipAddress);
            if (response.status === "success") {
                toast.success("Reason added successfully!");
                triggerMachineDetailRefresh();
            }

            if (response.status === "error") {
                triggerMachineDetailRefresh(); // Revert optimistic update
                toast.error("An Error Occurred. Unable to add reason");
            }
        } catch (error) {
            toast.error("An error Occurred" + error);
        }
    };

    function formatSecondsToTime(seconds: number) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return [hours, minutes, secs]
            .map((v) => String(v.toFixed(0)).padStart(2, "0"))
            .join(":");
    }

    function isMissingReason() {
        return (
            machineDetail?.MachineVorneDetails.Last_Down_Time_Info
                .Process_state_reason_display_name === "Missing Reason"
        );
    }

    function MissingDownReasonComponent() {
        return (
            <div key={"missingDownReasonComponent"}>
                <div className="flex justify-between">
                    <div className="p-2 w-7/12">Last Down Reason:</div>
                    <div
                        className={`p-2 w-5/12 ${isMissingReason() ? "bg-red-500 animate-pulse rounded-lg text-white" : ""}`}
                    >
                        {
                            machineDetail?.MachineVorneDetails
                                .Last_Down_Time_Info
                                .Process_state_reason_display_name
                        }
                    </div>
                </div>
                <div className="flex justify-between">
                    <div className="p-2 w-7/12"></div>
                    <div className="w-5/12 py-2">
                        {isMissingReason() && (
                            <Select
                                value={missingReasonSelect.value}
                                onValueChange={(value: string) =>
                                    addMissingDownReason(value)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="--Select a down reason--" />
                                </SelectTrigger>
                                <SelectContent>
                                    {deviceDownReasons.length > 0 &&
                                        deviceDownReasons.map((reason) => (
                                            <SelectItem
                                                key={reason.value}
                                                value={reason.value}
                                            >
                                                {reason.text}
                                            </SelectItem>
                                        ))}
                                </SelectContent>
                            </Select>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    const machineJobCounts = {
        Good:
            machineDetail?.MachineVorneDetails.Current_Job_Info.Good_count ?? 0,
        Reject:
            machineDetail?.MachineVorneDetails.Current_Job_Info.Reject_count ??
            0,
        Target:
            machineDetail?.MachineVorneDetails.Current_Job_Info.Target_count ??
            0,
        Total:
            machineDetail?.MachineVorneDetails.Current_Job_Info.Total_count ??
            0,
    };

    const machineJobCountsTotals = {
        Good:
            (machineDetail?.MachineVorneDetails.Current_Job_Info.Good_count ??
                0) + (machineDetail?.MachineVorneDetails.JobCounts.Good ?? 0),
        Reject:
            (machineDetail?.MachineVorneDetails.Current_Job_Info.Reject_count ??
                0) + (machineDetail?.MachineVorneDetails.JobCounts.Reject ?? 0),
        Target:
            (machineDetail?.MachineVorneDetails.Current_Job_Info.Target_count ??
                0) + (machineDetail?.MachineVorneDetails.JobCounts.Target ?? 0),
        Total:
            (machineDetail?.MachineVorneDetails.Current_Job_Info.Total_count ??
                0) + (machineDetail?.MachineVorneDetails.JobCounts.Total ?? 0),
    };

    const jobDetailsColumns = [
        {
            "Vorne Job": vorneJob,
            "SFIS OP Id": machineDetail?.EmpJobDetail.Id,
            "SFIS OP Name": machineDetail?.EmpJobDetail?.Employee?.Name,
            "SFIS Job": machineDetail?.EmpJobDetail.JobId,
            "Job Not Scheduled Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info
                    .Not_scheduled_time ?? 0,
            ),
            "Shift Not Scheduled Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Not_scheduled_time ?? 0,
            ),
        },
        {
            Machine: machineDetail?.EmpJobDetail.DeviceKey,
            "Reason Code":
                machineDetail?.MachineVorneDetails.Current_Event_Info
                    .Process_state_reason_display_name,
            "Part #": machineDetail?.EmpJobDetail.Job?.PartId,
            "Job Planned Stop Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info
                    .Planned_stop_time ?? 0,
            ),
            "Shift Planned Stop Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Planned_stop_time ?? 0,
            ),
        },
        {
            MissingDownReasonComponent: "MissingDownReasonComponent",
            "Last Down Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Last_Down_Time_Info
                    .Down_time ?? 0,
            ),
            "Job Down Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info.Down_time ??
                    0,
            ),
            "Shift Down Time": formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Down_time ?? 0,
            ),
        },
    ];

    const { isBigHeadMode } = useBigHeadModeStore();

    const jobDetails: { label: string; value: string | number | undefined }[] =
        [
            {
                label: "Vorne Job",
                value: machineDetail?.MachineVorneDetails.Current_Job_Info.Job,
            },
            { label: "Machine", value: machineDetail?.EmpJobDetail.DeviceKey },
            {
                label: "Last Down Reason",
                value: machineDetail?.MachineVorneDetails.Last_Down_Time_Info
                    .Process_state_reason_display_name,
            },
            { label: "SFIS OP Id", value: machineDetail?.EmpJobDetail.Id },
            {
                label: "SFIS OP Name",
                value: machineDetail?.EmpJobDetail?.Employee?.Name,
            },
            { label: "Part #", value: machineDetail?.EmpJobDetail.Job?.PartId },
            { label: "SFIS Job", value: machineDetail?.EmpJobDetail.JobId },
        ];

    const timeInfo: { label: string; value: string | number | undefined }[] = [
        {
            label: "Last Down Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Last_Down_Time_Info
                    .Down_time ?? 0,
            ),
        },
        {
            label: "Job Planned Stop Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info
                    .Planned_stop_time ?? 0,
            ),
        },
        {
            label: "Job Down Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info.Down_time ??
                    0,
            ),
        },
        {
            label: "Job Not Scheduled Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Job_Info
                    .Not_scheduled_time ?? 0,
            ),
        },
        {
            label: "Shift Planned Stop Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Planned_stop_time ?? 0,
            ),
        },
        {
            label: "Shift Down Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Down_time ?? 0,
            ),
        },
        {
            label: "Shift Not Scheduled Time",
            value: formatSecondsToTime(
                machineDetail?.MachineVorneDetails.Current_Shift_Info
                    .Not_scheduled_time ?? 0,
            ),
        },
    ];

    const JobDetailsItem = (item: {
        label: string;
        value: string | number | undefined;
    }) => {
        return (
            <div className="grid grid-cols-2 gap-2 border-b pb-2">
                <div className="font-medium">{item.label}</div>
                <div className="text-right">{item.value}</div>
            </div>
        );
    };

    const MobileLayout = () => {
        return (
            <Card className="shadow-sm">
                <h2 className="text-lg font-bold bg-gray-100 p-4">
                    Job Detail
                </h2>
                <CardContent className="p-0">
                    <Tabs defaultValue="details" className="w-full">
                        <TabsList className="grid w-full grid-cols-2 rounded-t-none bg-gray-100">
                            <TabsTrigger value="details">
                                Job Details
                            </TabsTrigger>
                            <TabsTrigger value="times">Time Info</TabsTrigger>
                        </TabsList>

                        <TabsContent value="details" className="space-y-4 p-4">
                            <>
                                {jobDetails.map((item) => (
                                    <JobDetailsItem
                                        key={item.label}
                                        label={item.label}
                                        value={item.value}
                                    />
                                ))}
                                <div className="grid grid-cols-2 gap-2 pb-2">
                                    <div className="font-medium">
                                        Reason Code
                                    </div>
                                    <div className="text-right">
                                        {
                                            machineDetail?.MachineVorneDetails
                                                .Last_Down_Time_Info
                                                .Process_state_reason_display_name
                                        }
                                    </div>
                                </div>

                                <Drawer
                                    open={isDownDrawerOpen}
                                    onOpenChange={setIsDownDrawerOpen}
                                >
                                    <DrawerTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className="w-full justify-between"
                                            aria-label="Select down reason"
                                        >
                                            {selectedDownReason ||
                                                "-- Select a down reason --"}
                                            <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                                        </Button>
                                    </DrawerTrigger>
                                    <DrawerContent aria-describedby={undefined}>
                                        <DrawerHeader>
                                            <DrawerTitle>
                                                Select Down Reason
                                            </DrawerTitle>
                                        </DrawerHeader>
                                        <ScrollArea className="h-[50vh] px-4">
                                            {/* TODO: Potentially expand to a handleSelectChange instead of just onValueChange */}
                                            <RadioGroup
                                                value={selectedDownReason}
                                                onValueChange={(value) => {
                                                    setSelectedDownReason(
                                                        value,
                                                    );
                                                    setIsDownDrawerOpen(false);
                                                }}
                                                className="space-y-3 py-2"
                                            >
                                                {deviceDownReasons.map(
                                                    (reason) => (
                                                        <div
                                                            key={
                                                                reason.value
                                                            }
                                                            className="flex items-center justify-between border-b pb-3 last:border-b-0"
                                                        >
                                                            <Label
                                                                htmlFor={
                                                                    reason.value
                                                                }
                                                                className="flex-1 cursor-pointer py-2"
                                                            >
                                                                {
                                                                    reason.text
                                                                }
                                                            </Label>
                                                            <RadioGroupItem
                                                                value={
                                                                    reason.text
                                                                }
                                                                id={
                                                                    reason.value
                                                                }
                                                                className="h-5 w-5"
                                                            />
                                                        </div>
                                                    ),
                                                )}
                                            </RadioGroup>
                                        </ScrollArea>
                                    </DrawerContent>
                                </Drawer>
                            </>
                        </TabsContent>
                        <TabsContent value="times" className="space-y-4 p-4">
                            {timeInfo.map((item) => (
                                <JobDetailsItem
                                    key={item.label}
                                    label={item.label}
                                    value={item.value}
                                />
                            ))}
                        </TabsContent>
                    </Tabs>

                    {/* Job Counts Section */}
                    <div className="border-t mt-4">
                        <Collapsible
                            className="w-full"
                            open={isJobCountOpen}
                            onOpenChange={setIsJobCountOpen}
                        >
                            <CollapsibleTrigger className="flex items-center justify-between w-full p-4 font-bold bg-slate-50">
                                <span>Job Count</span>
                                {isJobCountOpen ? (
                                    <ChevronDown className="h-4 w-4" />
                                ) : (
                                    <ChevronRight className="h-4 w-4" />
                                )}
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <div className="p-4 border-b">
                                    <div className="font-medium mb-2">
                                        Current Shift
                                    </div>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div>Target:</div>
                                        <div className="text-right">
                                            {machineJobCounts.Target.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Total:</div>
                                        <div className="text-right">
                                            {machineJobCounts.Total.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Good:</div>
                                        <div className="text-right">
                                            {machineJobCounts.Good.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Reject:</div>
                                        <div className="text-right">
                                            {machineJobCounts.Reject.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="p-4">
                                    <div className="font-medium mb-2">
                                        Total Job
                                    </div>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div>Target:</div>
                                        <div className="text-right">
                                            {machineJobCountsTotals.Target.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Total:</div>
                                        <div className="text-right">
                                            {machineJobCountsTotals.Total.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Good:</div>
                                        <div className="text-right">
                                            {machineJobCountsTotals.Good.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>

                                        <div>Reject:</div>
                                        <div className="text-right">
                                            {machineJobCountsTotals.Reject.toLocaleString(
                                                undefined,
                                                {
                                                    maximumFractionDigits: 2,
                                                },
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CollapsibleContent>
                        </Collapsible>
                    </div>
                </CardContent>
            </Card>
        );
    };

    if (isBigHeadMode) return <MobileLayout />;

    return (
        <Card className="w-full mx-auto">
            <h2 className="text-lg font-bold mb-4 bg-gray-100 p-4">
                Job Detail
            </h2>
            <div className="p-4">
                <div className="w-full m-auto grid md:grid-cols-3 grid-cols-1 gap-4">
                    {jobDetailsColumns.map((column, index) => {
                        return (
                            <div key={index}>
                                {Object.entries(column).map((jobDetail) => {
                                    if (
                                        jobDetail[1] ===
                                        "MissingDownReasonComponent"
                                    ) {
                                        return MissingDownReasonComponent();
                                    }
                                    return (
                                        <div
                                            className="justify-between w-full flex"
                                            key={jobDetail[0]}
                                        >
                                            <div className="p-2 w-7/12 whitespace-nowrap">
                                                {jobDetail[0]}:
                                            </div>
                                            <div className="p-2 text-left w-5/12 whitespace-nowrap">
                                                {jobDetail[1]}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        );
                    })}
                </div>
                <Separator className="bg-gray-200 h-[1px] my-8" />
                <div className="overflow-x-auto w-full">
                    <table className="w-full m-auto overflow-x-auto">
                        <thead>
                            <tr>
                                <th className="p-2 text-left">Job Counts</th>
                                <th className="p-2 text-left">Target</th>
                                <th className="p-2 text-left">Total</th>
                                <th className="p-2 text-left">Good</th>
                                <th className="p-2 text-left">Reject</th>
                            </tr>
                        </thead>
                        <tbody className="gap-4">
                            <tr>
                                <td className="p-2">Current Shift</td>
                                {Object.entries(machineJobCounts).map(
                                    (count) => {
                                        return (
                                            <td className="p-2" key={count[0]}>
                                                {count[1]
                                                    ? count[1].toLocaleString(
                                                          undefined,
                                                          {
                                                              maximumFractionDigits: 2,
                                                          },
                                                      )
                                                    : 0}
                                            </td>
                                        );
                                    },
                                )}
                            </tr>
                            <tr>
                                <td className="p-2">Total Job</td>
                                {Object.entries(machineJobCountsTotals).map(
                                    (count) => {
                                        return (
                                            <td className="p-2" key={count[0]}>
                                                {count[1]
                                                    ? count[1].toLocaleString(
                                                          undefined,
                                                          {
                                                              maximumFractionDigits: 2,
                                                          },
                                                      )
                                                    : 0}
                                            </td>
                                        );
                                    },
                                )}
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </Card>
    );
};

export default JobDetailCard;
