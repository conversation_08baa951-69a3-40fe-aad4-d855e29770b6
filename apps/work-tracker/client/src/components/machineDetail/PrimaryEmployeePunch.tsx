import { Card } from "../ui/card";
import { Button } from "../ui/button";
import useEmployeePunchStore from "@/stores/EmployeePunchStore";
import { formatDate } from "@/lib/formatDate";
import { JobPunch } from "@/features/devices/api";
import useMachineDetailStore from "@/stores/MachineDetailStore";
import { toast } from "sonner";

const PrimaryEmployeePunch = () => {
    const { openDialog } = useEmployeePunchStore();
    const { machineDetail, vorneJob, triggerMachineDetailRefresh } =
        useMachineDetailStore();

    const generalPunch = async (
        employeeId: string | undefined,
        jobId: string | undefined,
    ) => {
        if (!machineDetail || employeeId == undefined || jobId == undefined) {
            toast.error("Error", {
                description: "An error occurred",
            });
            return;
        }
        const response = await JobPunch(
            {
                EmployeeId: employeeId,
                JobId: jobId,
                OperatorType: "Primary",
                DeviceKey: machineDetail.EmpJobDetail.DeviceKey,
            },
            vorneJob,
        );
        return response.status;
    };

    const punchOut = async () => {
        const result = await generalPunch(
            "",
            machineDetail?.EmpJobDetail.JobId,
        );
        if (result == "success") {
            toast("Success", { description: "Employee Punched Out" });
            triggerMachineDetailRefresh();
        } else
            toast.error("Error", {
                description: "An error occurred",
            });
    };

    const punchOutEndJob = async () => {
        const result = await generalPunch("", "");
        if (result == "success") {
            toast("Success", {
                description: "Employee Punched Out & Ended Job",
            });
            triggerMachineDetailRefresh();
        } else
            toast.error("Error", {
                description: "An error occurred",
            });
    };

    const endJob = async () => {
        const result = await generalPunch(
            machineDetail?.EmpJobDetail.EmployeeId,
            "",
        );
        if (result == "success") {
            toast("Success", { description: "Ended Job" });
            triggerMachineDetailRefresh();
        } else
            toast.error("Error", {
                description: "An error occurred",
            });
    };

    return (
        <Card className="w-full mx-auto">
            <h2 className="text-lg font-bold mb-4 bg-gray-100 p-4">
                Primary Employee
            </h2>
            {machineDetail?.EmpJobDetail?.Employee && (
                <div className="flex justify-between items-center p-4 border-b">
                    <div>
                        <p className="font-semibold">
                            {machineDetail.EmpJobDetail.Employee.Name}
                        </p>
                        <p className="text-sm text-gray-500">
                            {machineDetail.EmpJobDetail.Employee.EmployeeId}
                        </p>
                        <p className="text-sm text-gray-500">
                            {formatDate(
                                new Date(
                                    machineDetail.EmpJobDetail.Employee.CreatedDate,
                                ),
                            )}
                        </p>
                    </div>
                </div>
            )}
            <div className="p-4">
                <Button
                    variant="default"
                    className="mr-4"
                    onClick={() => openDialog("Primary")}
                >
                    Punch In
                </Button>
                {machineDetail?.EmpJobDetail?.Employee ? (
                    <>
                        <Button
                            variant="destructive"
                            className="mr-4"
                            onClick={() => punchOut()}
                        >
                            Punch Out
                        </Button>

                        <Button
                            variant="destructive"
                            className="mr-4"
                            onClick={() => punchOutEndJob()}
                        >
                            Punch Out & End Job
                        </Button>

                        <Button
                            variant="destructive"
                            className="mr-4"
                            onClick={() => endJob()}
                        >
                            End Job
                        </Button>
                    </>
                ) : null}
            </div>
        </Card>
    );
};

export default PrimaryEmployeePunch;
