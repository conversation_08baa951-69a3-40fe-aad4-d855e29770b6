import useMachineDetailStore from "@/stores/MachineDetailStore";
import { Card, CardContent } from "../ui/card";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { DownReason } from "@/types/MachineDetailModels";
import { AddReason, GetDeviceDownReasons } from "@/features/devices/api";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../ui/select";
const ReasonStateCard = () => {
    const { machineDetail } = useMachineDetailStore();
    const [downReasons, setDownReasons] = useState<DownReason[]>([]);
    const [reason, setReason] = useState<string>();
    const fetchDownReasons = async () => {
        if (!machineDetail?.MachineVorneDetails.DeviceDetail?.DeviceKey) return;
        const response = await GetDeviceDownReasons(
            machineDetail?.MachineVorneDetails.DeviceDetail?.Device<PERSON>ey,
        );
        if (response.status === "success") {
            setDownReasons(response.payload);
        } else toast.error("Error", { description: response.status });
    };

    function GetReasonColor(reason: string | undefined) {
        switch (reason) {
            case "running":
                return "bg-green-400";
            case "down":
                return "bg-red-400";
            case "break":
                return "bg-blue-400";
            default:
                return "bg-orange-400";
        }
    }

    async function postReason(reason: string) {
        setReason(reason);
        const ipAddress =
            machineDetail?.MachineVorneDetails.DeviceDetail?.IpAddress;
        if (!ipAddress) {
            alert("Comment creation failed. No IP Address found.");
            return;
        }
        const response = await AddReason(reason, ipAddress);
        if (response.status === "success") {
            toast.success("Success", {
                description: response.payload,
            });
        } else toast.error("Error", { description: response.status });
    }

    useEffect(() => {
        fetchDownReasons();
    }, [machineDetail]);

    return (
        <Card className="shadow-sm">
            <h2 className="text-lg font-bold bg-gray-100 p-4">Job State</h2>
            <CardContent className="p-0">
                <div className="p-4 flex">
                    <div
                        className={
                            GetReasonColor(
                                machineDetail?.MachineVorneDetails
                                    .Current_Event_Info.Process_state,
                            ) + ` p-4 rounded-full text-xl`
                        }
                    >
                        {machineDetail?.MachineVorneDetails.Current_Event_Info.Process_state.toLocaleUpperCase()}
                    </div>
                    <div className="ml-4 min-w-48">
                        Reasons
                        <Select
                            onValueChange={(value: string) => {
                                postReason(value);
                            }}
                            value={reason}
                        >
                            <SelectTrigger value={reason}>
                                <SelectValue placeholder="-- Select a reason --" />
                            </SelectTrigger>
                            <SelectContent>
                                {downReasons.map((reason) => {
                                    if (
                                        reason === null ||
                                        reason.reasonText === undefined
                                    )
                                        return;
                                    return (
                                        <SelectItem
                                            key={reason.reasonCode}
                                            value={reason.reasonCode}
                                        >
                                            {reason.reasonText}
                                        </SelectItem>
                                    );
                                })}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ReasonStateCard;
