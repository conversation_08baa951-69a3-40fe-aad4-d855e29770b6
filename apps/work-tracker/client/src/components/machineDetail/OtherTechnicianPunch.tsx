import { Card } from "../ui/card";
import { But<PERSON> } from "../ui/button";
import { JobEmployee } from "@/types/DashboardModels";
import useEmployeePunchStore from "@/stores/EmployeePunchStore";
import { formatDate } from "@/lib/formatDate";
import useMachineDetailStore from "@/stores/MachineDetailStore";
import { PunchOut } from "@/features/devices/api";
import { toast } from "sonner";
import { useState } from "react";

const OtherTechnicianPunch = () => {
    const [idPunchOut, setIdPunchOut] = useState<number | undefined>();
    const { openDialog } = useEmployeePunchStore();
    const { machineDetail, triggerMachineDetailRefresh } =
        useMachineDetailStore();

    const handlePunchOut = async (id: number) => {
        setIdPunchOut(id);
        const response = await PunchOut(id);
        if (response.status === "success") {
            toast("Success", { description: "Employee Punched Out" });
            triggerMachineDetailRefresh();
        } else
            toast.error("Error", {
                description: "An error occured",
            });
    };
    function isEmpoyeePunchedOut(id: number) {
        return id === idPunchOut;
    }

    return (
        <Card className="w-full mx-auto">
            <h2 className="text-lg font-bold mb-4 bg-gray-100 p-4">
                Technicians
            </h2>
            {machineDetail?.TechPersonList !== undefined &&
                machineDetail?.TechPersonList.map(
                    (JobEmployee: JobEmployee) => (
                        <div
                            key={JobEmployee.EmployeeId}
                            className={`flex justify-between items-center p-4 border-b ${isEmpoyeePunchedOut(JobEmployee.Id) ? " opacity-60" : ""}`}
                        >
                            <div>
                                <p className="font-semibold">
                                    {JobEmployee.Employee?.Name}
                                </p>
                                <p className="text-sm text-gray-500">
                                    {JobEmployee.EmployeeId}
                                </p>
                                <p className="text-sm text-gray-500">
                                    {formatDate(
                                        new Date(JobEmployee.StartTime),
                                    )}
                                </p>
                            </div>
                            <Button
                                onClick={() => handlePunchOut(JobEmployee.Id)}
                                variant="destructive"
                                disabled={isEmpoyeePunchedOut(JobEmployee.Id)}
                                aria-label={`Punch Out Technician ${JobEmployee.Id}`}
                            >
                                Punch Out
                            </Button>
                        </div>
                    ),
                )}
            <div className="p-4">
                <Button
                    variant="default"
                    onClick={() => openDialog("Technician")}
                >
                    Add Technician
                </Button>
            </div>
        </Card>
    );
};

export default OtherTechnicianPunch;
