import { JobEmployee } from "./DashboardModels";

export interface VorneDevice {
    DeviceKey: string;
    IpAddress: string;
}

export interface MachineDetailViewModel {
    EmpJobDetail: JobEmployee;
    OtherEmployeeList: JobEmployee[];
    TechPersonList?: JobEmployee[];
    MachineVorneDetails: Resp_Machine_Current_Info;
}

export interface Resp_Machine_Current_Event_Info {
    Duration: number;
    Performance_impact: string;
    Process_state: string;
    Process_state_reason_display_name: string;
    Part_event_id: number;
}

export interface Resp_Machine_Current_Job_Info {
    Target_count: number;
    Total_count: number;
    Good_count: number;
    Reject_count: number;
    Not_scheduled_time: number;
    Down_time: number;
    Planned_stop_time: number;
    Job_event_id: number;
    Job: string;
}

export interface Resp_Machine_Current_Shift_Info {
    Down_time: number;
    Not_scheduled_time: number;
    Process_state_reason_display_name: string;
    Planned_stop_time: number;
    Shift_event_id: number;
}

export interface Resp_Machine_Last_Down_Time_Info {
    Down_time: number;
    Process_state: string;
    Process_state_reason_display_name: string;
}

export interface Resp_Vorne_Part_Comments {
    comment: string;
}

export interface JobCounts {
    Good: number;
    Reject: number;
    Total: number;
    Target: number;
}

export interface Resp_Machine_Current_Info {
    DeviceDetail?: VorneDevice;
    Current_Event_Info: Resp_Machine_Current_Event_Info;
    Current_Job_Info: Resp_Machine_Current_Job_Info;
    Current_Shift_Info: Resp_Machine_Current_Shift_Info;
    Last_Down_Time_Info: Resp_Machine_Last_Down_Time_Info;
    Part_CommentList: Resp_Vorne_Part_Comments[];
    JobCounts: JobCounts;
}

export interface Resp_Vorne_API_Field_Array {
    events: Array<Array<object>>;
}

export interface Error {
    code: string;
    message: string;
}

export interface Meta {
    assetId: string;
    dataVersion: string;
    deviceId: string;
    error: Error;
}

export interface Resp_Vorne_API_Root {
    data: Resp_Vorne_API_Field_Array;
    meta: Meta;
}

export interface EmployeesNotPunchedIntoJob {
    EmployeeType: number;
    DeviceKey: string;
    JobId: string;
}

export interface DownReason {
    deviceKey: string;
    reasonText: string;
    reasonCode: string;
    stateCode: string;
}

export interface MissingDownReason {
    disabled: boolean;
    group?: string;
    selected: boolean;
    text: string;
    value: string;
}
