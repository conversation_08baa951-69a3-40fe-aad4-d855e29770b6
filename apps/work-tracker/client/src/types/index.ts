export interface Part {
    id: number;
    //partId: string;
    partDescription: string;
    deviceKey: string;
    deviceType: string;

    idealCycleTime: number;
    taktTime: number;

    [key: `countMultiplier${number}`]: number;

    targetMultiplier: number;
    targetLaborPerPiece: number;
    downThreshold: number;
    startWithChangeover: boolean;
    changeoverTarget: number;
    changeoverReason: string;
    disableWhen: "running" | "changeover target" | "running or changeover target";

    createdAt: string;
    createdBy: string;
    isActive: boolean;
}

export interface Job {
    createdAt: string;
    createdBy: string;
    deviceKey: string;
    goalCount: number;
    isActive: boolean;
    //partJobDescription: string;
    partJobId: number;
    partId: number;
    //punches: unknown | null;
	//configType: "vorne" | "manual";
	expectedDuration: number | null;
}

// For Employee.punches
export interface JobEmployee {
    deviceKey: string;
    employeeId: string;
    operatorType: number;
    jobId: string;
    startTime: Date;
    endTime: Date | null;
    isComplete: boolean;
    lastModifiedBy: string | null;
    lastModifiedAt: Date | null;
    createdBy: string;
    createdAt: Date;
    employee?: Employee;
    job?: Job;
}

export interface APIEmployee {
    employeeId: string;
    name: string;
    type: number;
    isActive: boolean;
    notes?: string;
    inActiveDate?: Date | null;
    createdBy: string;
    createdDate: Date;
    punches?: JobEmployee[] | null;
}

// Use const enum to map number to string
export const EmployeeType = {
    1: "Employee",
    2: "Tech Employee",
} as const;
export type EmployeeType = (typeof EmployeeType)[keyof typeof EmployeeType];

export type Employee = Omit<APIEmployee, "type"> & {
    type: EmployeeType[keyof EmployeeType];
};
