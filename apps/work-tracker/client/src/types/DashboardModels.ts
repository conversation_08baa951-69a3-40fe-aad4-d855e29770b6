import { Job } from ".";
export type OperatorType = "Primary" | "Secondary" | "Technician";
// 0 is primary, 1 is secondary, 2 is technician
export type EmployeeType = "TechEmployee" | "Employee";

export interface SelectListItem {
    text: string;
    value: string;
}

export interface Employee {
    EmployeeId: string;
    Name: string;
    Type: EmployeeType;
    IsActive: boolean;
    Notes?: string;
    InActiveDate?: Date;
    CreatedBy: string;
    CreatedDate: Date;
    Punches?: JobEmployee[];
}

export interface EmployeeLowercase {
    employeeId: string;
    name: string;
    type: EmployeeType;
    isActive: boolean;
    notes?: string;
    inActiveDate?: Date;
    createdBy: string;
    createdDate: Date;
    punches?: JobEmployee[];
}

export interface JobEmployee {
    Id: number;
    DeviceKey: string;
    EmployeeId: string;
    OperatorType: OperatorType;
    JobId: string;
    StartTime: Date;
    EndTime?: Date;
    IsComplete: boolean;
    LastModifiedBy?: string;
    LastModifiedAt?: Date;
    CreatedBy: string;
    CreatedAt: Date;
    Employee?: Employee;
    Job?: Job;
}

export interface EmployeePunchView {
    operatorType: OperatorType;
    employeeList: SelectListItem[];
    activePunches: JobEmployee[];
    header: string;
    modalName: string;
}

export interface EmployeePunch {
    JobId: string;
    EmployeeId: string;
    OperatorType: string;
    DeviceKey: string;
}

export interface PunchOut {
    punchId: number;
}

export interface JobPunchResponse {
    jobId: string;
    employeeId: string;
    jobDescription?: string;
    employeeName?: string;
    partId?: string;
    partDescription?: string;
}

export interface VorneDeviceViewModel {
    deviceKey: string;
    vorneUrl: string;
}

export interface ErrorViewModel {
    requestId?: string;
    showRequestId: boolean;
}
