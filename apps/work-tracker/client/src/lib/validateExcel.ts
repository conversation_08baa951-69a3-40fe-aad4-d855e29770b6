import ExcelJS from "exceljs";

const MAX_SIZE_MB = 10;
const allowedExtensions = ["xls", "xlsx"];


const checkExcelHeaders = async (worksheet: ExcelJS.Worksheet, expectedHeaders: string[]) => {
    // Check if there column headers exist
    const firstRow = worksheet.getRow(1);
    if (!firstRow || firstRow.cellCount === 0)
        throw new Error("First row is empty");

    const headers: string[] = [];
    firstRow.eachCell((cell) => {
        headers.push(cell.value?.toString().trim() || "");
    });

    const missingHeaders = expectedHeaders.filter((h) => !headers.includes(h));

    if (missingHeaders.length > 0) {
        if (missingHeaders.length > 3) {
            throw new Error(
                `There are multiple missing headers in your Excel file. Please check against the template and try again.`,
            );
        } else
            throw new Error(
                `There are missing headers in your Excel file. Please check against the template and try again. Missing headers: ${missingHeaders.join(", ")}`,
            );
    }
};

const checkNoData = async (worksheet: ExcelJS.Worksheet) => {
    // Check if all rows after header are empty -- maybe reach a limit?
    const rowCount = worksheet.rowCount;
    let hasData = false;
    for (let rowNumber = 2; rowNumber <= rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);
        let rowHasData = false;
        row.eachCell((cell) => {
            if (
                cell.value !== null &&
                cell.value !== undefined &&
                cell.value !== ""
            ) {
                rowHasData = true;
            }
        });
        if (rowHasData) {
            hasData = true;
            break;
        }
    }

    if (!hasData) {
        throw new Error(
            "All rows after the header are empty. Please fill out the sheet and try again.",
        );
    }
};

async function validateExcel(file: File, expectedHeaders: string[]) {
    // Check for a defined file
    if (!file) throw new Error("Please submit an Excel file.");


    // Check if its the right extension
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
        throw Error("Invalid file format. Only Excel files are allowed.");
    }

    // Check if the file is possibly empty -- TODO: need to verify that this works
    if (file.size == 0) throw Error("File is empty");

    // Check if file size is too big
    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
        throw Error(`File size exceeds ${MAX_SIZE_MB}MB.`);
    }

    // Load the excel sheet in
    const arrayBuffer = await file.arrayBuffer();

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);

    // Grab the first worksheet
    const worksheet = workbook.worksheets[0];
    if (!worksheet) {
        throw new Error("No sheets found in the Excel file.");
    }

    // Check if the Excel structure is invalid
    await checkExcelHeaders(worksheet, expectedHeaders);

    // Check if there's no data
    await checkNoData(worksheet);

    // TODO: Sanitize inputs and remove suspcious characters and formulas
    // TODO: Reject files with hidden sheets, macros, or embedded objects

    return true;
}

export default validateExcel;
