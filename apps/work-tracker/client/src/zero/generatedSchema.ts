// Generated by Zero Schema Generator

import {
  table,
  string,
  boolean,
  number,
  json,
  enumeration,
  relationships,
  createSchema,
  type Row,
} from "@rocicorp/zero";

// Define tables

export const productionMetricsTable = table("production_metrics")
  .columns({
    start_time: number(),
    end_time: number(),
    device_id: number(),
    job_id: number(),
    part_id: number(),
    shift_id: number(),
    team_id: number(),
    reason_id: number(),
    shift_hour: string(),
    good_count: number(),
    reject_count: number(),
    expected_count: number(),
    target_count: number(),
    small_stops: number(),
    startup_rejects: number(),
    run_cycles: number(),
    partial_cycles: number(),
    ideal_time: number(),
    partial_cycle_lost_time: number(),
    small_stop_lost_time: number(),
    run_cycle_lost_time: number(),
  })
  .primaryKey("start_time", "device_id");

export const jobsTable = table("jobs")
  .columns({
    id: number(),
    part_id: number(),
    rework: boolean(),
    name: string(),
    is_active: boolean(),
    display_name: string(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const partsTable = table("parts")
  .columns({
    id: number(),
    name: string(),
    display_name: string(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const shiftsTable = table("shifts")
  .columns({
    id: number(),
    name: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const teamsTable = table("teams")
  .columns({
    id: number(),
    name: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const rejectCodesTable = table("reject_codes")
  .columns({
    id: number(),
    device_id: number(),
    name: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const reasonCodesTable = table("reason_codes")
  .columns({
    id: number(),
    device_id: number(),
    performance_impact: string(),
    performance_impact_display_name: string(),
    process_state: string(),
    process_state_display_name: string(),
    process_state_reason: string(),
    process_state_reason_display_name: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const devicesTable = table("devices")
  .columns({
    id: number(),
    name: string(),
    device_family: string(),
    ip_address: string(),
    firmware_version: string(),
    mac_address: string(),
    device_time: number(),
    asset_name: string(),
    can_have_queue: boolean(),
    enterprise: string(),
    plant: string(),
    area: string(),
    type: string(),
    sub_type: string(),
    line: string(),
    page: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const devicePropertyTable = table("device_property")
  .columns({
    device_id: number(),
    p_key: string(),
    p_value: string(),
  })
  .primaryKey("device_id", "p_key");

export const shiftEmailSettingsTable = table("shift_email_settings")
  .columns({
    id: number(),
    full_name: string(),
    email_address: string(),
    phone: string(),
    plant: string(),
    shift: string(),
    is_active: boolean(),
    deleted_at: number().optional(),
  })
  .primaryKey("id");

export const masterDowntimesTable = table("master_downtimes")
  .columns({
    id: number(),
    downtime_minutes: number(),
    wait_time: number(),
    last_email_date_time: number(),
    email_list: string(),
  })
  .primaryKey("id");

export const notificationRulesTable = table("notification_rules")
  .columns({
    id: number(),
    notification_group: string(),
    process_state_display_name: string(),
    process_state_reason_display_name: string(),
    comparison_operator: string(),
    notification_limit: number(),
    rule_type: number(),
    who: string(),
    squelch_time: number(),
    schedule_start: number(),
    schedule_end: number(),
    sunday_enabled: boolean(),
    monday_enabled: boolean(),
    tuesday_enabled: boolean(),
    wednesday_enabled: boolean(),
    thursday_enabled: boolean(),
    friday_enabled: boolean(),
    saturday_enabled: boolean(),
    last_emailed: number(),
    metric_type: number(),
    email_verified: boolean(),
    alert_type: number(),
    is_active: boolean(),
  })
  .primaryKey("id");

export const notificationSettingsTable = table("notification_settings")
  .columns({
    id: number(),
    host: string(),
    port: number(),
    username: string(),
    password: string(),
    from_address: string(),
    from_name: string(),
    subject: string(),
    twilio_ssid: string(),
    from_number: number(),
    is_anonymous: boolean(),
  })
  .primaryKey("id");

export const productionMetricExtraTable = table("production_metric_extra")
  .columns({
    start_time: number(),
    device_id: number(),
    month_event_id: number(),
    equipment_cycles: number(),
    target_cycles: number(),
    team_event_id: number(),
    shift_hour_event_id: number(),
    event_id: number(),
    hour_event_id: number(),
    process_state_event_id: number(),
    production_phase_event_id: number(),
    part_event_id: number(),
    job_event_id: number(),
    shift_event_id: number(),
    week_event_id: number(),
    quarter_event_id: number(),
    production_day_event_id: number(),
    information_source: string(),
    labor: number(),
    earned_labor: number(),
    modification_time: number(),
    pack_out_count: number(),
    record_id: number(),
    record_order: number(),
    record_version: number(),
    slice_order: number(),
    sync_id: number(),
    wip: number(),
    ordinal_shift: string(),
    run_cycle_lost_time: number(),
    year_event_id: number(),
    production_phase: string(),
    prodmetric_stream_key: number(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("start_time", "device_id");

export const notesTable = table("notes")
  .columns({
    inserted_date: number(),
    device_id: number(),
    parent_id: number().optional(),
    target_column_type: string(),
    target_column_value: number(),
    content: string(),
  })
  .primaryKey("inserted_date", "device_id");

export const notesExtraTable = table("notes_extra")
  .columns({
    inserted_date: number(),
    device_id: number(),
    record_id: number(),
    record_version: number(),
    slice_offset: number(),
    slice_order: number(),
    sync_id: number(),
    modification_time: number(),
    external_id: string(),
  })
  .primaryKey("inserted_date", "device_id");

export const scheduleEventsTable = table("schedule_events")
  .columns({
    id: number(),
    device_id: number(),
    job_id: number(),
    part_id: number(),
    rework: boolean(),
    max_changeover: number(),
    quantity: number(),
    start_time: number(),
    end_time: number(),
    created_at: number(),
    created_by: string(),
  })
  .primaryKey("id");

export const shiftSchedulesTable = table("shift_schedules")
  .columns({
    id: number(),
    device_id: number(),
    shift_id: number(),
    start_time: number(),
    end_time: number(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const partConfigsTable = table("part_configs")
  .columns({
    id: number(),
    device_type: string(),
    device_id: number(),
    part_id: number(),
    ideal_cycle_time: number(),
    takt_time: number(),
    count_multiplier1: number(),
    count_multiplier2: number(),
    count_multiplier3: number(),
    count_multiplier4: number(),
    count_multiplier5: number(),
    count_multiplier6: number(),
    count_multiplier7: number(),
    count_multiplier8: number(),
    target_multiplier: number(),
    target_labor_per_piece: number(),
    down_threshold: number().optional(),
    start_with_changeover: boolean(),
    changeover_target: number().optional(),
    changeover_reason: string().optional(),
    disable_when: string().optional(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const jobConfigsTable = table("job_configs")
  .columns({
    id: number(),
    job_id: number(),
    device_type: string(),
    device_id: number(),
    goal_count: number(),
    expected_duration: number().optional(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const employeesTable = table("employees")
  .columns({
    id: number(),
    external_id: string(),
    name: string(),
    type: number(),
    notes: string(),
    is_active: boolean(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("id");

export const jobPunchesTable = table("job_punches")
  .columns({
    start_time: number(),
    end_time: number().optional(),
    device_id: number(),
    employee_id: number(),
    job_id: number(),
    is_complete: boolean(),
    operator_type: number(),
    inserted_date: number(),
    inserted_by: string(),
  })
  .primaryKey("start_time", "device_id");

// Define relationships

export const productionMetricsTableRelationships = relationships(
  productionMetricsTable,
  ({ one }) => ({
    job: one({
      sourceField: ["job_id"],
      destField: ["id"],
      destSchema: jobsTable,
    }),
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
    part: one({
      sourceField: ["part_id"],
      destField: ["id"],
      destSchema: partsTable,
    }),
    shift: one({
      sourceField: ["shift_id"],
      destField: ["id"],
      destSchema: shiftsTable,
    }),
    team: one({
      sourceField: ["team_id"],
      destField: ["id"],
      destSchema: teamsTable,
    }),
    reason: one({
      sourceField: ["reason_id"],
      destField: ["id"],
      destSchema: reasonCodesTable,
    }),
    extra: one({
      sourceField: ["start_time", "device_id"],
      destField: ["start_time", "device_id"],
      destSchema: productionMetricExtraTable,
    }),
  }),
);

export const jobsTableRelationships = relationships(
  jobsTable,
  ({ one, many }) => ({
    part: one({
      sourceField: ["part_id"],
      destField: ["id"],
      destSchema: partsTable,
    }),
    production_metrics: many({
      sourceField: ["id"],
      destField: ["job_id"],
      destSchema: productionMetricsTable,
    }),
    schedule_events: many({
      sourceField: ["id"],
      destField: ["job_id"],
      destSchema: scheduleEventsTable,
    }),
    configs: many({
      sourceField: ["id"],
      destField: ["job_id"],
      destSchema: jobConfigsTable,
    }),
    punches: many({
      sourceField: ["id"],
      destField: ["job_id"],
      destSchema: jobPunchesTable,
    }),
  }),
);

export const partsTableRelationships = relationships(
  partsTable,
  ({ many }) => ({
    jobs: many({
      sourceField: ["id"],
      destField: ["part_id"],
      destSchema: jobsTable,
    }),
    production_metrics: many({
      sourceField: ["id"],
      destField: ["part_id"],
      destSchema: productionMetricsTable,
    }),
    schedule_events: many({
      sourceField: ["id"],
      destField: ["part_id"],
      destSchema: scheduleEventsTable,
    }),
    configs: many({
      sourceField: ["id"],
      destField: ["part_id"],
      destSchema: partConfigsTable,
    }),
  }),
);

export const shiftsTableRelationships = relationships(
  shiftsTable,
  ({ many }) => ({
    production_metrics: many({
      sourceField: ["id"],
      destField: ["shift_id"],
      destSchema: productionMetricsTable,
    }),
    shift_schedules: many({
      sourceField: ["id"],
      destField: ["shift_id"],
      destSchema: shiftSchedulesTable,
    }),
  }),
);

export const teamsTableRelationships = relationships(
  teamsTable,
  ({ many }) => ({
    production_metrics: many({
      sourceField: ["id"],
      destField: ["team_id"],
      destSchema: productionMetricsTable,
    }),
  }),
);

export const rejectCodesTableRelationships = relationships(
  rejectCodesTable,
  ({ one }) => ({
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
  }),
);

export const reasonCodesTableRelationships = relationships(
  reasonCodesTable,
  ({ one, many }) => ({
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
    reason_metrics: many({
      sourceField: ["id"],
      destField: ["reason_id"],
      destSchema: productionMetricsTable,
    }),
  }),
);

export const devicesTableRelationships = relationships(
  devicesTable,
  ({ many }) => ({
    production_metrics: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: productionMetricsTable,
    }),
    reject_codes: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: rejectCodesTable,
    }),
    reason_codes: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: reasonCodesTable,
    }),
    notes: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: notesTable,
    }),
    schedule_events: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: scheduleEventsTable,
    }),
    shift_schedules: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: shiftSchedulesTable,
    }),
    configs: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: jobConfigsTable,
    }),
    punches: many({
      sourceField: ["id"],
      destField: ["device_id"],
      destSchema: jobPunchesTable,
    }),
  }),
);

export const productionMetricExtraTableRelationships = relationships(
  productionMetricExtraTable,
  ({ one }) => ({
    production_metric: one({
      sourceField: ["start_time", "device_id"],
      destField: ["start_time", "device_id"],
      destSchema: productionMetricsTable,
    }),
  }),
);

export const notesTableRelationships = relationships(
  notesTable,
  ({ one, many }) => ({
    parent: one({
      sourceField: ["parent_id", "device_id"],
      destField: ["inserted_date", "device_id"],
      destSchema: notesTable,
    }),
    children: many({
      sourceField: ["inserted_date", "device_id"],
      destField: ["parent_id", "device_id"],
      destSchema: notesTable,
    }),
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
    extra: many({
      sourceField: ["inserted_date", "device_id"],
      destField: ["inserted_date", "device_id"],
      destSchema: notesExtraTable,
    }),
  }),
);

export const notesExtraTableRelationships = relationships(
  notesExtraTable,
  ({ one }) => ({
    note: one({
      sourceField: ["inserted_date", "device_id"],
      destField: ["inserted_date", "device_id"],
      destSchema: notesTable,
    }),
  }),
);

export const scheduleEventsTableRelationships = relationships(
  scheduleEventsTable,
  ({ one }) => ({
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
    job: one({
      sourceField: ["job_id"],
      destField: ["id"],
      destSchema: jobsTable,
    }),
    part: one({
      sourceField: ["part_id"],
      destField: ["id"],
      destSchema: partsTable,
    }),
  }),
);

export const shiftSchedulesTableRelationships = relationships(
  shiftSchedulesTable,
  ({ one }) => ({
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
    shift: one({
      sourceField: ["shift_id"],
      destField: ["id"],
      destSchema: shiftsTable,
    }),
  }),
);

export const partConfigsTableRelationships = relationships(
  partConfigsTable,
  ({ one }) => ({
    part: one({
      sourceField: ["part_id"],
      destField: ["id"],
      destSchema: partsTable,
    }),
  }),
);

export const jobConfigsTableRelationships = relationships(
  jobConfigsTable,
  ({ one }) => ({
    job: one({
      sourceField: ["job_id"],
      destField: ["id"],
      destSchema: jobsTable,
    }),
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
  }),
);

export const employeesTableRelationships = relationships(
  employeesTable,
  ({ many }) => ({
    punches: many({
      sourceField: ["id"],
      destField: ["employee_id"],
      destSchema: jobPunchesTable,
    }),
  }),
);

export const jobPunchesTableRelationships = relationships(
  jobPunchesTable,
  ({ one }) => ({
    employee: one({
      sourceField: ["employee_id"],
      destField: ["id"],
      destSchema: employeesTable,
    }),
    job: one({
      sourceField: ["job_id"],
      destField: ["id"],
      destSchema: jobsTable,
    }),
    device: one({
      sourceField: ["device_id"],
      destField: ["id"],
      destSchema: devicesTable,
    }),
  }),
);

// Define schema

export const schema = createSchema({
  tables: [
    productionMetricsTable,
    jobsTable,
    partsTable,
    shiftsTable,
    teamsTable,
    rejectCodesTable,
    reasonCodesTable,
    devicesTable,
    devicePropertyTable,
    shiftEmailSettingsTable,
    masterDowntimesTable,
    notificationRulesTable,
    notificationSettingsTable,
    productionMetricExtraTable,
    notesTable,
    notesExtraTable,
    scheduleEventsTable,
    shiftSchedulesTable,
    partConfigsTable,
    jobConfigsTable,
    employeesTable,
    jobPunchesTable,
  ],
  relationships: [
    productionMetricsTableRelationships,
    jobsTableRelationships,
    partsTableRelationships,
    shiftsTableRelationships,
    teamsTableRelationships,
    rejectCodesTableRelationships,
    reasonCodesTableRelationships,
    devicesTableRelationships,
    productionMetricExtraTableRelationships,
    notesTableRelationships,
    notesExtraTableRelationships,
    scheduleEventsTableRelationships,
    shiftSchedulesTableRelationships,
    partConfigsTableRelationships,
    jobConfigsTableRelationships,
    employeesTableRelationships,
    jobPunchesTableRelationships,
  ],
});

// Define types
export type Schema = typeof schema;
export type production_metrics = Row<typeof schema.tables.production_metrics>;
export type jobs = Row<typeof schema.tables.jobs>;
export type parts = Row<typeof schema.tables.parts>;
export type shifts = Row<typeof schema.tables.shifts>;
export type teams = Row<typeof schema.tables.teams>;
export type reject_codes = Row<typeof schema.tables.reject_codes>;
export type reason_codes = Row<typeof schema.tables.reason_codes>;
export type devices = Row<typeof schema.tables.devices>;
export type device_property = Row<typeof schema.tables.device_property>;
export type shift_email_settings = Row<
  typeof schema.tables.shift_email_settings
>;
export type master_downtimes = Row<typeof schema.tables.master_downtimes>;
export type notification_rules = Row<typeof schema.tables.notification_rules>;
export type notification_settings = Row<
  typeof schema.tables.notification_settings
>;
export type production_metric_extra = Row<
  typeof schema.tables.production_metric_extra
>;
export type notes = Row<typeof schema.tables.notes>;
export type notes_extra = Row<typeof schema.tables.notes_extra>;
export type schedule_events = Row<typeof schema.tables.schedule_events>;
export type shift_schedules = Row<typeof schema.tables.shift_schedules>;
export type part_configs = Row<typeof schema.tables.part_configs>;
export type job_configs = Row<typeof schema.tables.job_configs>;
export type employees = Row<typeof schema.tables.employees>;
export type job_punches = Row<typeof schema.tables.job_punches>;
