import {
    ANYONE_CAN_DO_ANYTHING,
    definePermissions,
    PermissionsConfig,
} from "@rocicorp/zero";

import { schema as AutoGeneratedSchema, Schema } from "./generatedSchema";

type AuthData = {
    sub: string | null;
};

export const permissions = definePermissions<AuthData, Schema>(
    AutoGeneratedSchema,
    () => {
        return {
            production_metrics: ANYONE_CAN_DO_ANYTHING,
            parts: ANYONE_CAN_DO_ANYTHING,
            jobs: ANYONE_CAN_DO_ANYTHING,
            job_configs: ANYONE_CAN_DO_ANYTHING,
            shifts: ANYONE_CAN_DO_ANYTHING,
            teams: ANYONE_CAN_DO_ANYTHING,
            reject_codes: ANYONE_CAN_DO_ANYTHING,
            reason_codes: ANYONE_CAN_DO_ANYTHING,
            devices: ANYONE_CAN_DO_ANYTHING,
            part_configs: ANYONE_CAN_DO_ANYTHING,
            device_property: ANYONE_CAN_DO_ANYTHING,
            shift_email_settings: ANYONE_CAN_DO_ANYTHING,
            master_downtimes: ANYONE_CAN_DO_ANYTHING,
            notification_rules: ANYONE_CAN_DO_ANYTHING,
            notification_settings: ANYONE_CAN_DO_ANYTHING,
            production_metric_extra: ANYONE_CAN_DO_ANYTHING,
            notes: ANYONE_CAN_DO_ANYTHING,
            notes_extra: ANYONE_CAN_DO_ANYTHING,
            schedule_events: ANYONE_CAN_DO_ANYTHING,
            shift_schedules: ANYONE_CAN_DO_ANYTHING,
            employees: ANYONE_CAN_DO_ANYTHING,
            job_punches: ANYONE_CAN_DO_ANYTHING,
        } satisfies PermissionsConfig<AuthData, Schema>;
    },
);
export const schema = AutoGeneratedSchema;
