"use client";

import { ZeroProvider } from "@rocicorp/zero/react";
import { Zero } from "@rocicorp/zero";
import Cookies from "js-cookie";
import { decodeJwt } from "jose";

import { schema } from "./schema";

const encodedJWT = Cookies.get("jwt");
const decodedJWT = encodedJWT && decodeJwt(encodedJWT);
const userID = decodedJWT?.sub ? (decodedJWT.sub as string) : "anon";

const z = new Zero({
    userID,
    auth: () => encodedJWT,
    server: "http://localhost:4848",
    schema,
    kvStore: "idb",
});

export default function ZeroProviderClient({
    children,
}: {
    children: React.ReactNode;
}) {
    return <ZeroProvider zero={z}>{children}</ZeroProvider>;
}
