import { MachineDetailViewModel } from "@/types/MachineDetailModels";
import { create } from "zustand";

interface MachineDetailStore {
    machineDetail: MachineDetailViewModel | null;
    refreshMachine: boolean;
    vorneJob: string;
    setMachineDetail: (value: MachineDetailViewModel) => void;
    setVorneJob: (value: string) => void;
    triggerMachineDetailRefresh: () => void;
}

const useMachineDetailStore = create<MachineDetailStore>((set) => ({
    machineDetail: null,
    refreshMachine: false,
    vorneJob: "",
    setMachineDetail: (value: MachineDetailViewModel) =>
        set({ machineDetail: value }),
    setVorneJob: (value: string) => set({ vorneJob: value }),
    triggerMachineDetailRefresh: () =>
        set((state) => ({ refreshMachine: !state.refreshMachine })),
}));

export default useMachineDetailStore;
