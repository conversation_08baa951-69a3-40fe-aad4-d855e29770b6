import { create } from "zustand";
import { persist } from "zustand/middleware";

interface BigHeadModeState {
    isBigHeadMode: boolean;
    toggleBigHead: () => void;
}

export const useBigHeadModeStore = create<BigHeadModeState>()(
    persist(
        (set) => ({
            isBigHeadMode: false,
            toggleBigHead: () =>
                set((state) => ({ isBigHeadMode: !state.isBigHeadMode })),
        }),
        { name: "big-head-mode" },
    ),
);
