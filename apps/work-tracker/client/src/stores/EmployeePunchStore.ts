import { OperatorType } from "@/types/DashboardModels";
import { create } from "zustand";

interface EmployeePunchState {
    isDialogOpen: boolean;
    operatorType: OperatorType;
    openDialog: (value: OperatorType) => void;
    closeDialog: () => void;
}

const useEmployeePunchStore = create<EmployeePunchState>((set) => ({
    isDialogOpen: false,
    operatorType: "Secondary",
    openDialog: (value: OperatorType) =>
        set({ isDialogOpen: true, operatorType: value }),
    closeDialog: () => set({ isDialogOpen: false }),
}));

export default useEmployeePunchStore;
