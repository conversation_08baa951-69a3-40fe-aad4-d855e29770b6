import { useEffect, useRef } from "react"

const useTitle = (title: string) => {
    const documentDefined = typeof document !== 'undefined'
    const originalTitle = useRef(documentDefined ? document.title : "")

    useEffect(() => {
        if (!documentDefined) return

        const prevTitle = originalTitle.current
        if (document.title !== title) document.title = title
        
        return () => {
            document.title = prevTitle
        }
        
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
}

export default useTitle