"use client";

import { useQuery, useZero } from "@rocicorp/zero/react";
import type { Schema } from "@/zero/generatedSchema";

/**
 * Hook to get the next ID for a given table.
 *
 * @param table The name of the table to get the next ID for.
 *
 * @returns An object with a `getNextId` function and an `isLoading` flag.
 */
export const useNextId = (table: keyof Schema["tables"]) => {
    const z = useZero();

    // Use the table parameter to query the specific table
    const [records] = useQuery(z.query[table].orderBy("id", "desc").limit(1));

    const getNextId = () => {
        if (!records || records.length === 0) return 1;
        return (records[0].id as number) + 1;
    };

    return {
        getNextId,
        isLoading: records === undefined,
    };
};
