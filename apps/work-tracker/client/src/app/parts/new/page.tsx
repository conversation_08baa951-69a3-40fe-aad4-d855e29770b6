"use client";

import { useRouter } from "next/navigation";

import PartsForm from "@/features/parts/components/form/PartsForm";
import useTitle from "@/hooks/useTitle";
import { PartFormData } from "@/features/parts/lib/validations";
import { usePartsCrud } from "@/features/parts/hooks/useParts";

export default function NewPartPage() {
    useTitle("Create New Part");
    const router = useRouter();
    const { createPart, isLoading } = usePartsCrud();

    // Handle form submission
    const handleSubmit = async (data: PartFormData) => {
        try {
            await createPart(data);
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    // Handle cancel button
    const handleCancel = () => {
        router.push("/parts");
    };

    return (
        <main className="w-full max-w-6xl p-8 mx-auto flex flex-col">
            <h1 className="font-semibold text-3xl pb-12">Create New Part</h1>
            <div className="w-full max-w-6xl ">
                <PartsForm
                    onSubmit={handleSubmit}
                    isSubmitting={isLoading}
                    onCancel={handleCancel}
                />
            </div>
        </main>
    );
}
