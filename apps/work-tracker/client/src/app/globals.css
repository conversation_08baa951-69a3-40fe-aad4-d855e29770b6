@import "tailwindcss";

@theme inline {
    --color-background: #ffffff;
    --color-foreground: #171717;
    --color-input: var(--color-gray-200);
    --color-primary: var(--color-blue-500);
    --color-primary-foreground: white;

    --color-destructive: hsl(0 84.2% 60.2%);
    --color-destructive-foreground: hsl(0 0% 98%);

    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);

    --color-card: hsl(0 0% 100%);
    --color-card-foreground: hsl(0 0% 3.9%);
    --color-popover: var(--color-background);
    --color-popover-foreground: hsl(0 0% 3.9%);
    --color-secondary: hsl(0 0% 96.1%);
    --color-secondary-foreground: hsl(0 0% 9%);
    --color-muted: hsl(0 0% 96.1%);
    --color-muted-foreground: hsl(0 0% 45.1%);
    --color-accent: hsl(0 0% 96.1%);
    --color-accent-foreground: hsl(0 0% 9%);
    --color-border: hsl(0 0% 89.8%);
    --color-input: hsl(0 0% 89.8%);
    --color-ring: hsl(0 0% 3.9%);
}

body {
    font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

input[type="file"]::file-selector-button {
  display: none;
}

/* Hides arrows on number inputs */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type=number] {
    -moz-appearance:textfield;
}
