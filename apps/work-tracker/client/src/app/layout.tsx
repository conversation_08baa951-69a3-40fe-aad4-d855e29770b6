import { StrictMode } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import Wrapper from '@/components/Wrapper'
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

type Props = Readonly<{ children: React.ReactNode }>
export default function RootLayout({ children }: Props) {
    return (
        <StrictMode>
            <html lang="en">
                <body>
                    <Wrapper>
                        {children}
                    </Wrapper>
                </body>
            </html>
        </StrictMode>
    );
}
