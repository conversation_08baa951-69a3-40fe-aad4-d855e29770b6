"use client";
import HomeCard from "@/components/nav/HomeCard";
import useTitle from "@/hooks/useTitle";

const HomePage = () => {
    useTitle("Home");

    return (
        <div className="w-full min-h-[calc(100vh-80px)] flex justify-center items-center p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 max-w-6xl w-full">
                <HomeCard title={"Devices"} />
                <HomeCard title={"Jobs"} />
                <HomeCard title={"Parts"} />
                <HomeCard title={"Employees"} />
            </div>
        </div>
    );
};

export default HomePage;
