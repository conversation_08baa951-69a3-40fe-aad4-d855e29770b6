'use client'
import useTitle from "@/hooks/useTitle";
import R from "react";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import PageLoading from '@/components/loading/PageLoading'
import { But<PERSON> } from "@/components/ui/button";
import { Device } from '@/app/api/devices/route'
import { useRouter } from 'next/navigation'
import { useZero, useQuery } from '@rocicorp/zero/react'

export type Status = { status: 'done', it: Device[] }
    | { status: 'error', error: unknown }
    | undefined

export default function() {
    useTitle("Machine List");

    return (
        <div className="container mx-auto py-6 px-4">
            <h1 className="text-2xl font-medium mb-6">
                List of Machines/Devices
            </h1>

            <div className="bg-white rounded-md shadow-sm overflow-hidden border border-gray-200">
                <Table className='border-border'>
                    <TableHeader>
                        <TableRow className="bg-gray-50">
                            <TableHead className="font-medium">
                                Machine name
                            </TableHead>
                            <TableHead className="w-80 text-right">
                                Actions
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <Content/>
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};

function Content() {
    const z = useZero()
    const [devices, status] = useQuery(z.query.devices)

    const router = useRouter();

    if(status.type === 'unknown') {
        return <TableRow className="border-t border-gray-200">
            <TableCell colSpan={2} className="text-center py-6">
                <PageLoading/>
            </TableCell>
        </TableRow>
    }
    else if(status.type === 'complete') {
        if(devices.length === 0) {
            return <TableRow>
                <TableCell
                    colSpan={2}
                    className="text-center py-6"
                >
                    No devices found
                </TableCell>
            </TableRow>
        }
        else {
            return devices.map((device) => (
                <TableRow
                    key={device.name}
                    className="border-t border-gray-200"
                >
                    <TableCell className="font-medium">
                        {device.name}
                    </TableCell>
                    <TableCell className="text-right space-x-2">
                        <Button
                            size="sm"
                            className="bg-indigo-600 hover:bg-indigo-700"
                            onClick={() =>
                                router.push("/device/MachineDetail/" + device.name)
                            }
                        >
                            Run
                        </Button>
                        <Button
                            size="sm"
                            variant="outline"
                            className="border-gray-300 text-gray-700"
                            onClick={() => window.open('http://' + device.ip_address)}
                        >
                            Vorne Board
                        </Button>
                    </TableCell>
                </TableRow>
            ))
        }
    }
    else {
        // TODO, is there even an error status?
        return <TableRow className="border-t border-gray-200">
            <TableCell colSpan={2} className="text-center py-6 text-destructive">
                Error: Unknown error
            </TableCell>
        </TableRow>
    }
}
