"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import JobsTable from "@/features/jobs/components/table/JobsTable";
// import UploadExcelFile from "@/features/jobs/UploadExcelFile";
import useTitle from "@/hooks/useTitle";
import { useRouter } from "next/navigation";
import { useState } from "react";

function JobsPage() {
    const router = useRouter();
    const [searchValue, setSearchValue] = useState("");
    useTitle("Manage Jobs");

    return (
        <main className="w-full max-w-7xl p-4 sm:p-6 lg:p-8 mx-auto flex flex-col">
            <div className="w-full flex flex-col items-start pb-4 gap-y-6 sm:gap-y-8 lg:gap-y-12">
                <h1 className="font-semibold text-2xl sm:text-3xl">
                    Manage Jobs
                </h1>

                <div className="w-full flex flex-col sm:flex-row items-start sm:items-center gap-3 flex-1">
                    <Button
                        onClick={() => router.push("/jobs/new")}
                        className="w-full sm:w-auto"
                    >
                        Add New Job
                    </Button>
                    {/* <UploadExcelFile /> */}

                    <Input
                        type="text"
                        placeholder="Search jobs..."
                        value={searchValue}
                        onChange={(event) => setSearchValue(event.target.value)}
                    />
                </div>
            </div>
            <div className="w-full">
                <JobsTable searchValue={searchValue} />
            </div>
        </main>
    );
}

export default JobsPage;
