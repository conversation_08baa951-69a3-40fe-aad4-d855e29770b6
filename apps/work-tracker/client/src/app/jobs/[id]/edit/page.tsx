"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useQuery, useZero } from "@rocicorp/zero/react";

import JobsForm from "@/features/jobs/components/form/JobsForm";
import useTitle from "@/hooks/useTitle";
import { JobFormData } from "@/features/jobs/lib/validations";
import { useJobCrud } from "@/features/jobs/hooks/useJobs";
import { devices, job_configs, parts } from "@/zero/generatedSchema";

export default function JobEditPage() {
    useTitle("Edit Job");
    const params = useParams();
    const router = useRouter();
    const z = useZero();
    const { updateJob, isLoading } = useJobCrud();
    const [jobId, setJobId] = useState<number | null>(null);
    const [initialValues, setInitialValues] = useState<JobFormData | undefined>(
        undefined,
    );

    // Parse job ID from URL params
    useEffect(() => {
        if (params?.id) {
            const id = Number(params.id);
            if (!isNaN(id)) {
                setJobId(id);
            } else {
                console.error("Invalid job ID:", params.id);
            }
        }
    }, [params, router]);

    // Fetch job, parts, and devices data
    const [job, jobStatus] = useQuery(
        z.query.jobs.where("id", "=", jobId).related("configs").one(),
    );
    const [partsData, partsStatus] = useQuery(z.query.parts.related("configs"));
    const [devicesData, devicesStatus] = useQuery(z.query.devices);

    // Prepare initial form values when job data is loaded
    useEffect(() => {
        if (job && jobStatus.type === "complete" && devicesData) {
            setInitialValues({
                partId: job.part_id,
                name: job.name,
                displayName: job.display_name,
                rework: job.rework,
                isActive: job.is_active,
                configs:
                    job.configs?.map((config: job_configs) => ({
                        deviceId: config.device_id,
                        deviceType: config.device_type,
                        goalCount: config.goal_count,
                        expectedDuration: config.expected_duration,
                    })) || [],
            });
        }
    }, [job, jobStatus.type]);

    // Handle form submission
    const handleSubmit = async (data: JobFormData) => {
        if (!jobId) return;

        try {
            await updateJob(
                jobId,
                data,
                job?.configs as unknown as job_configs[],
            );
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    // Handle cancel button
    const handleCancel = () => {
        router.push("/jobs");
    };

    // Show loading state if job data is still loading
    if (
        jobStatus.type === "unknown" ||
        partsStatus.type === "unknown" ||
        devicesStatus.type === "unknown"
    ) {
        return (
            <div className="flex items-center justify-center h-screen">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading job...</span>
            </div>
        );
    }

    // Show error state if job not found
    if (
        jobStatus.type === "complete" &&
        devicesStatus.type === "complete" &&
        partsStatus.type === "complete" &&
        !job
    ) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <h2 className="text-2xl font-semibold mb-2">
                        Job Not Found
                    </h2>
                    <p className="text-gray-600 mb-4">
                        The job you're looking for doesn't exist.
                    </p>
                    <button
                        onClick={() => router.push("/jobs")}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Back to Jobs
                    </button>
                </div>
            </div>
        );
    }

    return (
        <main className="w-full max-w-6xl p-8 mx-auto flex flex-col">
            <h1 className="font-semibold text-3xl pb-12">Edit Job</h1>
            <div className="w-full max-w-6xl ">
                {initialValues && (
                    <JobsForm
                        initialValues={initialValues}
                        availableParts={partsData as unknown as parts[]}
                        availableDevices={devicesData as unknown as devices[]}
                        onSubmit={handleSubmit}
                        isSubmitting={isLoading}
                        onCancel={handleCancel}
                    />
                )}
            </div>
        </main>
    );
}
