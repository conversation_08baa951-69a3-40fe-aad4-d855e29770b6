"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useZero } from "@rocicorp/zero/react";
import { toast } from "sonner";

import { PartFormData, PartConfigData } from "@/features/parts/lib/validations";
import { parts, part_configs, schema } from "@/zero/generatedSchema";
import { useNextId } from "../../../hooks/useNextId";

export const usePartsCrud = () => {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();
    const z = useZero();
    const { getNextId: getNextPartId } = useNextId("parts");
    const { getNextId: getNextPartConfigId } = useNextId("part_configs");

    // Helper function to insert part configs
    const insertPartConfigs = async (
        partId: number,
        configs: PartConfigData[],
    ) => {
        for (const config of configs) {
            // Get the latest part config ID and increment by 1
            const newConfigId = getNextPartConfigId() + 1;

            await z.mutate.part_configs.insert({
                id: newConfigId,
                part_id: partId,
                device_type: config.deviceType,
                device_id: config.deviceId,
                ideal_cycle_time: config.idealCycleTime,
                takt_time: config.taktTime,
                count_multiplier1: config.countMultiplier1,
                count_multiplier2: config.countMultiplier2,
                count_multiplier3: config.countMultiplier3,
                count_multiplier4: config.countMultiplier4,
                count_multiplier5: config.countMultiplier5,
                count_multiplier6: config.countMultiplier6,
                count_multiplier7: config.countMultiplier7,
                count_multiplier8: config.countMultiplier8,
                target_multiplier: config.targetMultiplier,
                target_labor_per_piece: config.targetLaborPerPiece,
                down_threshold: config.downThreshold,
                start_with_changeover: config.startWithChangeover,
                changeover_target: config.changeoverTarget,
                changeover_reason: config.changeoverReason || "",
                disable_when: config.disableWhen || "",
                is_active: true,
                inserted_date: Date.now(),
                inserted_by: "Current User", // TODO: Get from auth context
            });
        }
    };

    // Create a new part
    const createPart = async (data: PartFormData) => {
        setIsLoading(true);
        try {
            // Get the latest part ID and increment by 1
            const newPartId = getNextPartId() + 1;

            // Create part using Zero mutation
            await z.mutate.parts.insert({
                id: newPartId,
                name: data.name,
                display_name: data.displayName || data.name,
                inserted_date: Date.now(),
                inserted_by: "Current User", // TODO: Get from auth context
            });

            // Insert configs if any
            if (data.configs && data.configs.length > 0) {
                await insertPartConfigs(newPartId, data.configs);
            }

            toast.success(`Successfully created part "${data.name}"`);
            router.push("/parts");
            return true;
        } catch (error) {
            console.error("Error creating part:", error);
            toast.error(`Failed to create part "${data.name}"`);
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Update an existing part
    const updatePart = async (
        id: number,
        data: PartFormData,
        existingConfigs?: part_configs[],
    ) => {
        setIsLoading(true);
        try {
            // Update part using Zero mutation
            await z.mutate.parts.update({
                id: id,
                name: data.name,
                display_name: data.displayName || data.name,
            });

            // Handle configs update
            // First, delete existing configs
            if (existingConfigs && existingConfigs.length > 0) {
                for (const config of existingConfigs) {
                    await z.mutate.part_configs.delete({ id: config.id });
                }
            }

            // Then, insert new configs
            if (data.configs && data.configs.length > 0) {
                await insertPartConfigs(id, data.configs);
            }

            toast.success(`Successfully updated part "${data.name}"`);
            router.push("/parts");
            return true;
        } catch (error) {
            console.error("Error updating part:", error);
            toast.error(`Failed to update part "${data.name}"`);
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Delete a part
    const deletePart = async (part: parts) => {
        try {
            await z.mutate.parts.delete({ id: part.id });

            toast.success(`Successfully deleted part "${part.name}"`);
            return true;
        } catch (error) {
            console.error("Error deleting part:", error);
            toast.error(`Failed to delete part "${part.name}"`);
            throw error;
        }
    };

    // Navigate to part details/edit
    const viewPartDetails = (partId: number) => {
        router.push(`/parts/${partId}/edit`);
    };

    return {
        createPart,
        updatePart,
        deletePart,
        viewPartDetails,
        isLoading,
    };
};
