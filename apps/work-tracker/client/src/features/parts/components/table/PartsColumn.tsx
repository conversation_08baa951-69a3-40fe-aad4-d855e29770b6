import HeaderCell from "@/components/table/HeaderCell";
import { createColumnHelper } from "@tanstack/react-table";
import { parts } from "@/zero/generatedSchema";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Edit, Trash2 } from "lucide-react";
import { usePartsCrud } from "@/features/parts/hooks/useParts";

const columnHelper = createColumnHelper<parts>();

const getPartsColumns = () => {
    const { viewPartDetails, deletePart } = usePartsCrud();

    return [
        columnHelper.accessor("name", {
            header: ({ column }) => (
                <HeaderCell
                    label="Name"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="text-center text-wrap w-[172px]">
                    {getValue()}
                </div>
            ),
        }),
        columnHelper.accessor("display_name", {
            header: ({ column }) => (
                <HeaderCell
                    label="Display Name"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="text-center">{getValue()}</div>
            ),
        }),
        columnHelper.accessor("inserted_date", {
            header: ({ column }) => (
                <HeaderCell
                    label="Created Date"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => {
                const date = new Date(getValue());
                return (
                    <div className="text-center">
                        {date.toLocaleDateString()}
                    </div>
                );
            },
        }),
        columnHelper.accessor("inserted_by", {
            header: ({ column }) => (
                <HeaderCell
                    label="Created By"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="text-center">{getValue()}</div>
            ),
        }),
        columnHelper.display({
            id: "configs_count",
            header: () => (
                <span className="flex justify-center">Configurations</span>
            ),
            cell: ({ row }) => {
                const configs = (row.original as any).configs || [];
                return (
                    <div className="text-center">
                        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                            {configs.length} config
                            {configs.length !== 1 ? "s" : ""}
                        </span>
                    </div>
                );
            },
        }),
        columnHelper.display({
            id: "actions",
            header: () => <span className="flex justify-center">Actions</span>,
            cell: ({ row }) => (
                <div className="text-center">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                                onClick={() => viewPartDetails(row.original.id)}
                            >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Part
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                onClick={() => deletePart(row.original)}
                                className="text-red-600"
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Part
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            ),
        }),
    ];
};

export default getPartsColumns;
