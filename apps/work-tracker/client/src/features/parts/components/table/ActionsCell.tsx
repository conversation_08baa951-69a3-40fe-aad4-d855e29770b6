import { Button } from "@/components/ui/button"
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Part } from "@/types"
import { MoreHorizontal } from "lucide-react"

interface ActionsCellProps {
	part: Part
	onRemove: (part: Part) => void
	onEditOpen: React.Dispatch<React.SetStateAction<boolean>>
	setSelectedPart: React.Dispatch<React.SetStateAction<Part | undefined>>
}

const ActionsCell = ({
	part,
	onRemove,
	onEditOpen,
	setSelectedPart,
}: ActionsCellProps) => {
	const handleEditPart = () => {
		setSelectedPart(part)
		onEditOpen(true)
	}

	const handleRemovePart = () => {
		onRemove(part)
	}

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<MoreHorizontal />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuItem onClick={handleEditPart}>Edit</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					onClick={handleRemovePart}
					className="text-destructive focus:text-destructive focus:bg-destructive/10"
				>
					Remove
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	)
}

export default ActionsCell
