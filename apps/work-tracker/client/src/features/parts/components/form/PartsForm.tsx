"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useQuery, useZero } from "@rocicorp/zero/react";

import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import {
    partFormSchema,
    type PartFormData,
} from "@/features/parts/lib/validations";
import { devices } from "@/zero/generatedSchema";
import { PartInfoSection } from "./PartInfoSection";
import { PartConfigSection } from "./PartConfigSection";

interface PartsFormProps {
    initialValues?: PartFormData;
    onSubmit: (data: PartFormData) => Promise<void>;
    isSubmitting?: boolean;
    onCancel?: () => void;
}

export default function PartsForm({
    initialValues,
    onSubmit,
    isSubmitting = false,
    onCancel,
}: PartsFormProps) {
    const z = useZero();

    // Fetch devices data
    const [devicesData, devicesStatus] = useQuery(z.query.devices);

    // Create form with default values
    const form = useForm<PartFormData>({
        resolver: zodResolver(partFormSchema),
        defaultValues: initialValues || {
            name: "",
            displayName: "",
            configs: [],
        },
        mode: "onChange",
    });

    // Watch all form values to detect changes
    const isDirty = form.formState.isDirty;

    // Reset form when initialValues change
    useEffect(() => {
        if (initialValues) {
            form.reset(initialValues);
        }
    }, [initialValues, form]);

    const handleSubmit = async (data: PartFormData) => {
        try {
            await onSubmit(data);
        } catch (error) {
            console.error("Form submission error:", error);
        }
    };

    // Show loading state if devices are still loading
    if (devicesStatus.type === "unknown") {
        return (
            <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading devices...</span>
            </div>
        );
    }

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="space-y-8"
            >
                <PartInfoSection control={form.control} />

                <PartConfigSection
                    control={form.control}
                    availableDevices={devicesData as unknown as devices[]}
                />

                <div className="flex flex-col sm:flex-row items-center justify-end space-y-2 sm:space-y-0 sm:space-x-4 pt-6">
                    {onCancel && (
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onCancel}
                            className="w-full sm:w-auto"
                        >
                            Cancel
                        </Button>
                    )}
                    {initialValues && (
                        <Button
                            type="submit"
                            disabled={isSubmitting || !isDirty}
                            className="w-full sm:w-auto"
                        >
                            {isSubmitting && (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            Update Part
                        </Button>
                    )}
                    {!initialValues && (
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full sm:w-auto"
                        >
                            {isSubmitting && (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            Create Part
                        </Button>
                    )}
                </div>
            </form>
        </Form>
    );
}
