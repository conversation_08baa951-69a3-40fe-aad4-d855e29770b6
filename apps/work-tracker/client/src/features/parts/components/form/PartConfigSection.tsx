"use client";

import React from "react";
import { Control, useFieldArray, useWatch } from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";

import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

import { PartFormData } from "@/features/parts/lib/validations";
import { devices } from "@/zero/generatedSchema";
import { DEVICE_LIST } from "@/lib/device-lists";

interface PartConfigSectionProps {
    control: Control<PartFormData>;
    availableDevices: devices[];
}

export function PartConfigSection({
    control,
    availableDevices,
}: PartConfigSectionProps) {
    const { fields, append, remove } = useFieldArray({
        control,
        name: "configs",
    });

    const addConfiguration = () => {
        append({
            deviceType: "",
            deviceId: 0,
            idealCycleTime: 0,
            taktTime: 0,
            countMultiplier1: 0,
            countMultiplier2: 0,
            countMultiplier3: 0,
            countMultiplier4: 0,
            countMultiplier5: 0,
            countMultiplier6: 0,
            countMultiplier7: 0,
            countMultiplier8: 0,
            targetMultiplier: 0,
            targetLaborPerPiece: 0,
            downThreshold: undefined,
            startWithChangeover: false,
            changeoverTarget: undefined,
            changeoverReason: "",
            disableWhen: "running",
        });
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Part Configurations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                {fields.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                        No configurations added yet. Click "Add Configuration"
                        to get started.
                    </p>
                ) : (
                    fields.map((field, index) => (
                        <Card key={field.id} className="relative">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg">
                                        Configuration {index + 1}
                                    </CardTitle>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => remove(index)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Device Selection */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={control}
                                        name={`configs.${index}.deviceType`}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>
                                                    Device Type *
                                                </FormLabel>
                                                <Select
                                                    onValueChange={
                                                        field.onChange
                                                    }
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select device" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {DEVICE_LIST?.length >
                                                        0 ? (
                                                            DEVICE_LIST.map(
                                                                (device) => (
                                                                    <SelectItem
                                                                        key={
                                                                            device.deviceKey
                                                                        }
                                                                        value={
                                                                            device.deviceKey
                                                                        }
                                                                    >
                                                                        {
                                                                            device.deviceType
                                                                        }{" "}
                                                                        -{" "}
                                                                        {
                                                                            device.deviceKey
                                                                        }
                                                                    </SelectItem>
                                                                ),
                                                            )
                                                        ) : (
                                                            <SelectItem value="no-device">
                                                                No devices
                                                                available
                                                            </SelectItem>
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={control}
                                        name={`configs.${index}.deviceId`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : "";
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Device *
                                                    </FormLabel>
                                                    <Select
                                                        onValueChange={(
                                                            value,
                                                        ) =>
                                                            field.onChange(
                                                                Number(value),
                                                            )
                                                        }
                                                        value={value}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select a device" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {availableDevices.map(
                                                                (device) => (
                                                                    <SelectItem
                                                                        key={
                                                                            device.id
                                                                        }
                                                                        value={device.id.toString()}
                                                                    >
                                                                        {
                                                                            device.name
                                                                        }
                                                                    </SelectItem>
                                                                ),
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />
                                </div>

                                {/* Timing Configuration */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={control}
                                        name={`configs.${index}.idealCycleTime`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : "";
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Ideal Cycle Time *
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            placeholder="0.00"
                                                            {...field}
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    Number(
                                                                        e.target
                                                                            .value,
                                                                    ),
                                                                )
                                                            }
                                                            value={value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />

                                    <FormField
                                        control={control}
                                        name={`configs.${index}.taktTime`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : "";
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Takt Time *
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            placeholder="0.00"
                                                            {...field}
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    Number(
                                                                        e.target
                                                                            .value,
                                                                    ),
                                                                )
                                                            }
                                                            value={value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />
                                </div>

                                {/* Count Multipliers - First Row */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                    {[1, 2, 3, 4].map((num) => (
                                        <FormField
                                            key={num}
                                            control={control}
                                            name={
                                                `configs.${index}.countMultiplier${num}` as any
                                            }
                                            render={({ field }) => {
                                                // Ensure field.value is a valid number
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : 1;
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Count Multiplier{" "}
                                                            {num}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                step="0.01"
                                                                placeholder="0.00"
                                                                {...field}
                                                                onChange={(e) =>
                                                                    field.onChange(
                                                                        Number(
                                                                            e
                                                                                .target
                                                                                .value,
                                                                        ),
                                                                    )
                                                                }
                                                                value={value}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />
                                    ))}
                                </div>

                                {/* Count Multipliers - Second Row */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                    {[5, 6, 7, 8].map((num) => (
                                        <FormField
                                            key={num}
                                            control={control}
                                            name={
                                                `configs.${index}.countMultiplier${num}` as any
                                            }
                                            render={({ field }) => {
                                                // Ensure field.value is a valid number
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : 1;
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Count Multiplier{" "}
                                                            {num}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                step="0.01"
                                                                placeholder="0.00"
                                                                {...field}
                                                                onChange={(e) =>
                                                                    field.onChange(
                                                                        Number(
                                                                            e
                                                                                .target
                                                                                .value,
                                                                        ),
                                                                    )
                                                                }
                                                                value={value}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />
                                    ))}
                                </div>

                                {/* Target Configuration */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <FormField
                                        control={control}
                                        name={`configs.${index}.targetMultiplier`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : 1;
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Target Multiplier
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            placeholder="0.00"
                                                            {...field}
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    Number(
                                                                        e.target
                                                                            .value,
                                                                    ),
                                                                )
                                                            }
                                                            value={value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />

                                    <FormField
                                        control={control}
                                        name={`configs.${index}.targetLaborPerPiece`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : "";
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Target Labor Per Piece
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            placeholder="0.00"
                                                            {...field}
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    Number(
                                                                        e.target
                                                                            .value,
                                                                    ),
                                                                )
                                                            }
                                                            value={value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />

                                    <FormField
                                        control={control}
                                        name={`configs.${index}.downThreshold`}
                                        render={({ field }) => {
                                            // Ensure field.value is a valid number
                                            const value =
                                                typeof field.value ===
                                                    "number" &&
                                                !isNaN(field.value) &&
                                                field.value > 0
                                                    ? field.value.toString()
                                                    : "";
                                            return (
                                                <FormItem>
                                                    <FormLabel>
                                                        Down Threshold
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            placeholder="0.00"
                                                            {...field}
                                                            onChange={(e) =>
                                                                field.onChange(
                                                                    Number(
                                                                        e.target
                                                                            .value,
                                                                    ) ||
                                                                        undefined,
                                                                )
                                                            }
                                                            value={value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }}
                                    />
                                </div>

                                {/* Changeover Configuration */}
                                <div className="space-y-4">
                                    <FormField
                                        control={control}
                                        name={`configs.${index}.startWithChangeover`}
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={
                                                            field.onChange
                                                        }
                                                    />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                    <FormLabel>
                                                        Start With Changeover
                                                    </FormLabel>
                                                </div>
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={control}
                                            name={`configs.${index}.changeoverTarget`}
                                            render={({ field }) => {
                                                // Ensure field.value is a valid number
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : "";
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Changeover Target
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                step="0.01"
                                                                placeholder="0.00"
                                                                {...field}
                                                                onChange={(
                                                                    e,
                                                                ) => {
                                                                    const val =
                                                                        e.target
                                                                            .value;
                                                                    field.onChange(
                                                                        val ===
                                                                            ""
                                                                            ? undefined
                                                                            : Number(
                                                                                  val,
                                                                              ),
                                                                    );
                                                                }}
                                                                value={value}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />

                                        <FormField
                                            control={control}
                                            name={`configs.${index}.changeoverReason`}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>
                                                        Changeover Reason
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="Enter changeover reason"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <FormField
                                        control={control}
                                        name={`configs.${index}.disableWhen`}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>
                                                    Disable When
                                                </FormLabel>
                                                <Select
                                                    onValueChange={
                                                        field.onChange
                                                    }
                                                    value={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select disable condition" />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        <SelectItem value="running">
                                                            Running
                                                        </SelectItem>
                                                        <SelectItem value="changeover target">
                                                            Changeover Target
                                                        </SelectItem>
                                                        <SelectItem value="running or changeover target">
                                                            Running or
                                                            Changeover Target
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </CardContent>
            <CardFooter>
                <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addConfiguration}
                    className="w-full"
                >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Configuration
                </Button>
            </CardFooter>
        </Card>
    );
}
