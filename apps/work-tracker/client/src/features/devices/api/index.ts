import { EmployeePunch, EmployeeType } from "@/types/DashboardModels";
import { EmployeesNotPunchedIntoJob } from "@/types/MachineDetailModels";
import { apiUrl, newApiUrl } from '@/lib/serverUrl'
import { Output } from '@/app/api/devices/route'

const API_URL = new URL("Device", apiUrl);

export async function getDevices(signal?: AbortSignal) {
    const resp = await fetch(new URL('devices', newApiUrl), { signal })
    if(!resp.ok) {
        throw new Error(
            'Response status: ' + resp.status
                + '\nResponse: ' + await resp.text().catch(e => e)
        )
    }
    return (await resp.json()) as Output
}

interface SuccessResponse<T> {
    status: "success";
    payload: T;
}
interface ErrorResponse {
    status: "error";
    message: string;
}

interface DownReason {
    reasonName: string;
    deviceIp: string;
}

type Response<T> = ErrorResponse | SuccessResponse<T>;

export async function getAllDevices<T>(): Promise<Response<T>> {
    try {
        const response = await fetch(API_URL);
        const data = await response.json();
        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        if (err instanceof Error) {
            return {
                status: "error",
                message: err.message,
            };
        } else {
            return {
                status: "error",
                message:
                    "An error occurred on the server. Please try again later",
            };
        }
    }
}

export async function getAllJobs<T>(): Promise<Response<T>> {
    try {
        const response = await fetch(API_URL + "/GetAllJobs");
        const data = await response.json();
        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        if (err instanceof Error) {
            return {
                status: "error",
                message: err.message,
            };
        } else {
            return {
                status: "error",
                message:
                    "An error occurred on the server. Please try again later",
            };
        }
    }
}

export async function JobPunch(PunchData: EmployeePunch, vorneJob?: string) {
    try {
        const response = await fetch(API_URL + "/JobPunch", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ ...PunchData, vorneJob: vorneJob ?? "" }),
        });

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function AddEmployeeToJob(PunchData: EmployeePunch) {
    try {
        const response = await fetch(API_URL + "/AddEmployee", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(PunchData),
        });

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function AddDownReason(config: DownReason) {
    try {
        const response = await fetch(
            API_URL +
                `/DownReasonChangeEvent?reasonName=${config.reasonName}&deviceIp=${config.deviceIp}`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(config),
            },
        );

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function AddReason(reasonName: string, deviceIp: string) {
    try {
        const response = await fetch(
            API_URL +
                `/ReasonChangeEvent?reasonName=${reasonName}&deviceIp=${deviceIp}`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    reasonName: reasonName,
                    deviceIp: deviceIp,
                }),
            },
        );

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: await response.text(),
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function PunchOut(PunchId: number) {
    try {
        const response = await fetch(API_URL + "/EmpJobPunchOut", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ PunchId: PunchId }),
        });

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function GetMachineDetail(DeviceKey: string) {
    try {
        const response = await fetch(API_URL + `/MachineDetail/${DeviceKey}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        if (err instanceof Error) {
            return {
                status: "error",
                message: err.message,
            };
        } else {
            return {
                status: "error",
                message:
                    "An error occurred on the server. Please try again later",
            };
        }
    }
}

export async function GetEmployeeList(employeeType: EmployeeType) {
    try {
        const response = await fetch(
            API_URL + `/EmployeeList/${employeeType}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            },
        );
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function GetEmployeesNotPunchedIntoJob(
    EmployeesNotPunchedIntoJob: EmployeesNotPunchedIntoJob,
) {
    try {
        const response = await fetch(API_URL + `/EmployeesNotPunchedIntoJob`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(EmployeesNotPunchedIntoJob),
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function GetDeviceDownReasons(deviceCode: string) {
    try {
        const response = await fetch(API_URL + `/Reasons/` + deviceCode, {
            method: "GET",
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function GetMissingDeviceDownReasons(deviceCode: string) {
    try {
        const response = await fetch(
            API_URL + `/MachineDetail/MissingDownReasons/` + deviceCode,
            {
                method: "GET",
            },
        );
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        return handleError(err);
    }
}

export async function AddMissingDownReason(
    reasonName: string,
    ipAddress: string,
) {
    try {
        const response = await fetch(API_URL + `/DownReasonChangeEvent`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                ReasonName: reasonName,
                DeviceIP: ipAddress,
            }),
        });

        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
        };
    } catch (err: unknown) {
        return handleError(err);
    }
}

export async function GetVorneJob(boardIp: string) {
    try {
        const response = await fetch(
            `http://${boardIp}/api/v0/channels/job/events/current?fields=job`,
            {
                method: "GET",
            },
        );
        const data = await response.json();
        if (!response.ok) {
            throw new Error(`Error: ${response}`);
        }

        return {
            status: "success",
            payload: data,
        };
    } catch (err) {
        return handleError(err);
    }
}

function handleError(err: unknown): ErrorResponse {
    if (err instanceof Error) {
        return {
            status: "error",
            message: err.message,
        };
    } else {
        return {
            status: "error",
            message: "An error occurred on the server. Please try again later",
        };
    }
}
