"use client";

import { useState } from "react";

import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogTrigger,
    <PERSON>alogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from "@/components/ui/dialog";
import { Upload } from "lucide-react";
import { insertJobsFromExcel } from "./lib/excelt-file-convertor";
import { useZero } from "@rocicorp/zero/react";

const UploadExcelFile = () => {
    const [file, setFile] = useState<File | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const z = useZero();
    const route = window.location.origin;

    const handleSubmit = async () => {
        setError(null);
        setIsLoading(true);
        if (!file) {
            setIsLoading(false);
            return;
        }

        const toastId = toast.loading("Importing jobs from Excel file...");
        try {
            const resp = await insertJobsFromExcel(file, z);

            if (resp.status !== "success") throw new Error(resp.message);

            // Success! Navigate back
            toast.success(resp.message || "New jobs have been created", {
                id: toastId,
            });
            console.log("Successfully imported jobs:", resp.results);
            setIsOpen(false);
            setFile(null); // Reset file selection
        } catch (error) {
            if (error instanceof Error) {
                setError(error.message);
                toast.error(error.message, { id: toastId });
            } else {
                setError(
                    "A problem occurred processing the file. Please try again later",
                );
                toast.error("Failed to create new jobs. Please try again.", {
                    id: toastId,
                });
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleFile = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setFile(file);
            // Remove any error states
            setError(null);
        }
    };

    const handleOpen = (isOpen: boolean) => {
        setIsOpen(!isOpen);
        setError(null);
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={() => handleOpen(isOpen)}>
                <DialogTrigger asChild>
                    <Button variant={"outline"}>
                        <Upload className="mr-2 size-4" />
                        Import from Excel
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Upload Jobs from Excel</DialogTitle>
                        <DialogDescription className="text-gray-800">
                            Upload an Excel file to import data into the table.
                            Download a template{" "}
                            <a
                                href={`${route}/api/jobs/excel`}
                                target="_blank"
                                className="inline-block hover:underline text-blue-500"
                            >
                                here.
                            </a>
                        </DialogDescription>
                    </DialogHeader>
                    <div
                        className={
                            "rounded border-dashed border-4 flex border-gray-400 relative h-64"
                        }
                    >
                        <input
                            type="file"
                            accept=".xlsx"
                            onChange={handleFile}
                            className="w-full cursor-pointer z-10 bg-gray-100 opacity-0"
                        />
                        <div className="absolute w-full h-full flex justify-center items-center">
                            <h1
                                className={
                                    "text-xl font-semibold text-gray-400"
                                }
                            >
                                {file ? file.name : "Click To Select"}
                            </h1>
                        </div>
                    </div>
                    {error && (
                        <p className=" bg-red-300 text-red-800 text-balance rounded-lg border border-red-500 py-2 px-4">
                            {error}
                        </p>
                    )}
                    <DialogFooter>
                        <Button
                            type="submit"
                            onClick={handleSubmit}
                            disabled={!file || isLoading}
                        >
                            {isLoading ? "Importing..." : "Import Data"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default UploadExcelFile;
