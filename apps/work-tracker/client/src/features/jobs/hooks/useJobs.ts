import { useState } from "react";
import { toast } from "sonner";
import { useZero } from "@rocicorp/zero/react";
import { job_configs, jobs } from "@/zero/generatedSchema";
import { JobFormData } from "@/features/jobs/lib/validations";
import { useRouter } from "next/navigation";
import { useNextId } from "@/hooks/useNextId";

// Define an extended type that includes the configs relationship
export interface JobWithConfigs extends jobs {
    configs?: job_configs[];
}

export const useJobCrud = () => {
    const [isLoading, setIsLoading] = useState(false);
    const z = useZero();
    const router = useRouter();
    const { getNextId: getNextJobId } = useNextId("jobs");
    const { getNextId: getNextJobConfigId } = useNextId("job_configs");

    // Create a new job
    const createJob = async (data: JobFormData) => {
        setIsLoading(true);
        try {
            // Get the latest job ID and increment by 1
            const newJobId = getNextJobId() + 1;

            // Create job using Zero mutation
            await z.mutate.jobs.insert({
                id: newJobId,
                part_id: data.partId,
                name: data.name,
                display_name: data.displayName || data.name,
                rework: data.rework,
                is_active: data.isActive,
                inserted_date: Date.now(),
                inserted_by: "Current User", // TODO: Get from auth context
            });

            // Insert configs if any
            if (data.configs && data.configs.length > 0) {
                await insertJobConfigs(newJobId, data.configs);
            }

            toast.success("Job created successfully!");
            return newJobId;
        } catch (error) {
            console.error("Failed to create job:", error);
            toast.error(
                `Failed to create job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            router.push("/jobs");
            setIsLoading(false);
        }
    };

    // Update an existing job
    const updateJob = async (
        id: number,
        data: JobFormData,
        existingConfigs?: job_configs[],
    ) => {
        setIsLoading(true);
        try {
            // Update job using Zero mutation
            await z.mutate.jobs.update({
                id: id,
                part_id: data.partId,
                name: data.name,
                display_name: data.displayName || data.name,
                rework: data.rework,
                is_active: data.isActive,
            });

            // Handle configs update
            // First, delete existing configs
            if (existingConfigs && existingConfigs.length > 0) {
                for (const config of existingConfigs) {
                    await z.mutate.job_configs.delete({ id: config.id });
                }
            }

            // Then insert new configs
            if (data.configs && data.configs.length > 0) {
                await insertJobConfigs(id, data.configs);
            }

            toast.success("Job updated successfully!");
            return id;
        } catch (error) {
            console.error("Failed to update job:", error);
            toast.error(
                `Failed to update job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            router.push("/jobs");
            setIsLoading(false);
        }
    };

    // Helper function to insert job configs
    const insertJobConfigs = async (
        jobId: number,
        configs: JobFormData["configs"],
    ) => {
        for (const config of configs) {
            // Get the latest job config ID and increment by 1
            const newConfigId = getNextJobConfigId() + 1;

            await z.mutate.job_configs.insert({
                id: newConfigId,
                job_id: jobId,
                device_type: config.deviceType,
                device_id: config.deviceId,
                goal_count: config.goalCount,
                expected_duration: config.expectedDuration,
                is_active: true,
                inserted_date: Date.now(),
                inserted_by: "Current User",
            });
        }
    };

    // Delete a job
    const deleteJob = async (job: JobWithConfigs) => {
        setIsLoading(true);
        try {
            // First, delete all associated job configs
            if (job.configs && job.configs.length > 0) {
                for (const config of job.configs) {
                    await z.mutate.job_configs.delete({
                        id: config.id,
                    });
                }
            }

            // Then delete the job itself
            await z.mutate.jobs.delete({
                id: job.id,
            });
            toast.success(`Successfully deleted ${job.name}`);
            return true;
        } catch (error) {
            console.error("Failed to delete job:", error);
            toast.error(
                `Failed to delete job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Toggle job active status
    const toggleJobStatus = async (job: jobs) => {
        try {
            await z.mutate.jobs.update({
                id: job.id,
                is_active: !job.is_active,
            });

            toast.success(
                `Successfully ${job.is_active ? "deactivated" : "activated"} ${job.name}`,
            );
            return true;
        } catch (error) {
            console.error("Error updating job status:", error);
            toast.error(
                `Failed to ${job.is_active ? "deactivate" : "activate"} ${job.name}`,
            );
            throw error;
        }
    };

    // Navigate to job details
    const viewJobDetails = (jobId: number) => {
        router.push(`/jobs/${jobId}/edit`);
    };

    return {
        createJob,
        updateJob,
        deleteJob,
        toggleJobStatus,
        viewJobDetails,
        isLoading,
    };
};
