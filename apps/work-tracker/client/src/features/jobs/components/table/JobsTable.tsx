"use client";

import { DataTable, DataTableStatus } from "@/components/ui/data-table";
import React from "react";
import getJobsColumns from "./JobsColumn";
import { useQuery, useZero } from "@rocicorp/zero/react";
import { jobs, job_configs } from "@/zero/generatedSchema";
import { ColumnDef } from "@tanstack/react-table";

// Define the same interface here to ensure consistency
interface JobWithConfigs extends jobs {
    configs?: job_configs[];
}

interface JobsTableProps {
    initialJobs?: JobWithConfigs[];
    searchValue?: string;
}

export const JobsTable = ({
    initialJobs = [],
    searchValue = "",
}: JobsTableProps) => {
    const z = useZero();
    const [jobs, status] = useQuery(
        z.query.jobs.related("configs").orderBy("inserted_date", "desc"),
    );

    // Cast the jobs data to the JobWithConfigs type
    const typedJobs = (jobs || []) as unknown as JobWithConfigs[];

    // Use initialJobs for initial render, then use jobs from query when available
    const displayJobs = typedJobs.length > 0 ? typedJobs : initialJobs;

    // Cast the columns to the correct type
    const columns = getJobsColumns() as ColumnDef<JobWithConfigs, unknown>[];

    // Determine the table status
    let tableStatus: DataTableStatus = "success";
    if (status.type === "unknown") {
        tableStatus = "loading";
    } else if (displayJobs.length === 0) {
        tableStatus = "empty";
    }

    return (
        <>
            <DataTable
                columns={columns}
                data={displayJobs}
                status={tableStatus}
                filterValue={searchValue}
            />
        </>
    );
};

export default JobsTable;
