import { createColumnHelper } from "@tanstack/react-table";
import Header<PERSON>ell from "@/components/table/HeaderCell";
import { jobs, job_configs } from "@/zero/generatedSchema";
import JobActionsCell from "./JobActionsCell";

// Define an extended type that includes the configs relationship
interface JobWithConfigs extends jobs {
    configs?: job_configs[];
}

const columnHelper = createColumnHelper<JobWithConfigs>();

export const getJobsColumns = () => {
    return [
        columnHelper.accessor("name", {
            header: ({ column }) => (
                <HeaderCell
                    label="Name"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="font-medium text-center">{getValue()}</div>
            ),
        }),
        columnHelper.accessor("display_name", {
            header: ({ column }) => (
                <HeaderCell
                    label="Display Name"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="font-medium text-center">{getValue()}</div>
            ),
        }),
        columnHelper.accessor(
            (row) => {
                // Safely access the goal_count from the first config
                return row.configs?.[0]?.goal_count;
            },
            {
                id: "goalCount",
                header: ({ column }) => (
                    <HeaderCell
                        label="Goal Count"
                        column={column}
                        className="flex justify-center"
                    />
                ),
                cell: ({ getValue }) => {
                    const value = getValue();
                    return (
                        <div className="text-center">
                            {value ? value.toLocaleString() : "N/A"}
                        </div>
                    );
                },
            },
        ),
        columnHelper.accessor(
            (row) => {
                return row.configs?.[0]?.expected_duration;
            },
            {
                id: "expectedDuration",
                header: ({ column }) => (
                    <HeaderCell
                        label="Expected Duration (min)"
                        column={column}
                        className="flex justify-center"
                    />
                ),
                cell: ({ getValue }) => {
                    const duration = getValue() as number | null;
                    return duration ? (
                        <div className="text-center">
                            {duration.toLocaleString()}
                        </div>
                    ) : (
                        <div className="text-center text-gray-500">Not Set</div>
                    );
                },
            },
        ),
        columnHelper.accessor("is_active", {
            header: ({ column }) => (
                <HeaderCell
                    label="Status"
                    column={column}
                    className="text-center"
                />
            ),
            cell: ({ row }) => {
                const active = row.getValue("is_active");
                return (
                    <div
                        className={`${active ? "text-green-600" : "text-red-600"} text-center`}
                    >
                        {active ? "Active" : "Inactive"}
                    </div>
                );
            },
        }),
        columnHelper.accessor("inserted_date", {
            header: ({ column }) => (
                <HeaderCell
                    label="Created On"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => {
                const dateValue = getValue();
                const date = new Date(dateValue);
                return (
                    <div className="text-center">
                        {date.toLocaleDateString("en-US", {
                            month: "long",
                            day: "numeric",
                            year: "numeric",
                        })}
                    </div>
                );
            },
        }),
        columnHelper.accessor("inserted_by", {
            header: ({ column }) => (
                <HeaderCell
                    label="Created By"
                    column={column}
                    className="flex justify-center"
                />
            ),
            cell: ({ getValue }) => (
                <div className="text-center">{getValue()}</div>
            ),
        }),
        columnHelper.display({
            id: "actions",
            header: () => <span className="flex justify-center">Actions</span>,
            cell: ({ row }) => <JobActionsCell job={row.original} />,
        }),
    ];
};

export default getJobsColumns;
