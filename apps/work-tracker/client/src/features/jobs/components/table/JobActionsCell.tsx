import { useState } from "react";
import { <PERSON><PERSON><PERSON>zon<PERSON>, Trash2, Eye, Power, PowerOff } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { useJobCrud, JobWithConfigs } from "@/features/jobs/hooks/useJobs";

interface JobActionsCellProps {
    job: JobWithConfigs;
}

const JobActionsCell = ({ job }: JobActionsCellProps) => {
    const { deleteJob, toggleJobStatus, viewJobDetails, isLoading } =
        useJobCrud();
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    const handleViewDetails = () => {
        viewJobDetails(job.id);
    };

    const handleToggleStatus = async () => {
        try {
            await toggleJobStatus(job);
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    const handleDeleteJob = async () => {
        setIsDeleting(true);
        try {
            await deleteJob(job);
            setShowDeleteDialog(false);
        } catch (error) {
            // Error is already handled in the hook
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <div className="text-center">
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        onClick={handleViewDetails}
                        className="text-blue-600 focus:text-blue-600 focus:bg-blue-50"
                    >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        onClick={handleToggleStatus}
                        className={
                            job.is_active
                                ? "text-amber-600 focus:text-amber-600 focus:bg-amber-50"
                                : "text-green-600 focus:text-green-600 focus:bg-green-50"
                        }
                    >
                        {job.is_active ? (
                            <>
                                <PowerOff className="h-4 w-4 mr-2" />
                                Deactivate
                            </>
                        ) : (
                            <>
                                <Power className="h-4 w-4 mr-2" />
                                Activate
                            </>
                        )}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        onClick={() => setShowDeleteDialog(true)}
                        className="text-red-600 focus:text-red-600 focus:bg-red-50"
                    >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete the job "{job.name}
                            "? This action cannot be undone and will remove all
                            associated configurations.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="gap-2 sm:gap-0">
                        <Button
                            variant="outline"
                            onClick={() => setShowDeleteDialog(false)}
                            disabled={isDeleting}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteJob}
                            disabled={isDeleting}
                        >
                            {isDeleting ? "Deleting..." : "Delete Job"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default JobActionsCell;
