import React from "react";
import { Control } from "react-hook-form";
import { Package } from "lucide-react";
import { JobFormData } from "@/features/jobs/lib/validations";
import { parts } from "@/zero/generatedSchema";

import {
    FormField,
    FormItem,
    FormLabel,
    FormControl,
    FormMessage,
    FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface JobInfoSectionProps {
    control: Control<JobFormData>;
    availableParts: parts[];
}

export const JobInfoSection = ({
    control,
    availableParts,
}: JobInfoSectionProps) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Job Information
                </CardTitle>
                <CardDescription>
                    Basic details about the job including name, part
                    association, and status.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Part Selection */}
                    <FormField
                        control={control}
                        name="partId"
                        render={({ field }) => {
                            // Ensure field.value is a valid number
                            const value =
                                typeof field.value === "number" &&
                                !isNaN(field.value)
                                    ? field.value.toString()
                                    : "";

                            return (
                                <FormItem>
                                    <FormLabel>Part *</FormLabel>
                                    <Select
                                        onValueChange={(value) => {
                                            const numValue = parseInt(
                                                value,
                                                10,
                                            );
                                            field.onChange(numValue);
                                        }}
                                        value={value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a part" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {availableParts?.map((part) => (
                                                <SelectItem
                                                    key={part.id}
                                                    value={part.id.toString()}
                                                    className="flex justify-between w-full"
                                                >
                                                    {part.display_name ||
                                                        part.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormDescription>
                                        Select the part this job will produce.
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            );
                        }}
                    />

                    {/* Job Name */}
                    <FormField
                        control={control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Job Name *</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter job name"
                                        {...field}
                                    />
                                </FormControl>
                                <FormDescription>
                                    A unique name to identify this job.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Display Name */}
                    <FormField
                        control={control}
                        name="displayName"
                        render={({ field }) => (
                            <FormItem className="h-fit">
                                <FormLabel>Display Name</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter display name (optional)"
                                        {...field}
                                    />
                                </FormControl>
                                <FormDescription>
                                    Optional friendly name for display purposes.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <div className="space-y-4">
                        {/* Rework Checkbox */}
                        <FormField
                            control={control}
                            name="rework"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-gray-200 p-4">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>Rework Job</FormLabel>
                                        <FormDescription>
                                            Mark this job as a rework operation.
                                        </FormDescription>
                                    </div>
                                </FormItem>
                            )}
                        />

                        {/* Active Status Checkbox */}
                        <FormField
                            control={control}
                            name="isActive"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-gray-200 p-4">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>Active Job</FormLabel>
                                        <FormDescription>
                                            Enable this job for production
                                            scheduling.
                                        </FormDescription>
                                    </div>
                                </FormItem>
                            )}
                        />
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};
