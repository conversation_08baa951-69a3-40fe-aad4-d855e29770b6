import React from "react";
import { Control, FieldArrayWithId } from "react-hook-form";
import { Plus, Trash2, Info, Settings } from "lucide-react";
import { JobFormData } from "@/features/jobs/lib/validations";
import { devices } from "@/zero/generatedSchema";

import {
    FormField,
    FormItem,
    FormLabel,
    FormControl,
    FormMessage,
    FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { DEVICE_LIST } from "@/lib/device-lists";

interface JobConfigSectionProps {
    control: Control<JobFormData>;
    availableDevices: devices[];
    addConfiguration: () => void;
    fields: FieldArrayWithId<JobFormData, "configs", "id">[];
    remove: (index: number) => void;
}

export const JobConfigSection = ({
    control,
    availableDevices,
    addConfiguration,
    fields,
    remove,
}: JobConfigSectionProps) => {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Device Configurations
                </CardTitle>
                <CardDescription>
                    Configure devices and production targets for this job.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {fields.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                        <Info className="h-8 w-8 mx-auto mb-2" />
                        <p>No device configurations added yet.</p>
                        <p className="text-sm">
                            Add configurations to specify production targets and
                            device assignments.
                        </p>
                    </div>
                ) : (
                    <>
                        {fields.map((field, index) => (
                            <Card key={field.id} className="border-dashed">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">
                                            Configuration {index + 1}
                                        </CardTitle>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => remove(index)}
                                            className="text-destructive hover:text-destructive"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={control}
                                            name={`configs.${index}.deviceType`}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>
                                                        Device Type *
                                                    </FormLabel>
                                                    <Select
                                                        onValueChange={
                                                            field.onChange
                                                        }
                                                        value={field.value}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select device" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            {DEVICE_LIST?.length >
                                                            0 ? (
                                                                DEVICE_LIST.map(
                                                                    (
                                                                        device,
                                                                    ) => (
                                                                        <SelectItem
                                                                            key={
                                                                                device.deviceKey
                                                                            }
                                                                            value={
                                                                                device.deviceKey
                                                                            }
                                                                        >
                                                                            {
                                                                                device.deviceType
                                                                            }{" "}
                                                                            -{" "}
                                                                            {
                                                                                device.deviceKey
                                                                            }
                                                                        </SelectItem>
                                                                    ),
                                                                )
                                                            ) : (
                                                                <SelectItem value="no-device">
                                                                    No devices
                                                                    available
                                                                </SelectItem>
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={control}
                                            name={`configs.${index}.deviceId`}
                                            render={({ field }) => {
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : "";
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Device Name *
                                                        </FormLabel>
                                                        <Select
                                                            onValueChange={(
                                                                value,
                                                            ) => {
                                                                const numValue =
                                                                    parseInt(
                                                                        value,
                                                                        10,
                                                                    );
                                                                field.onChange(
                                                                    numValue,
                                                                );
                                                            }}
                                                            value={value}
                                                        >
                                                            <FormControl>
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder="Select device" />
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                {availableDevices?.length >
                                                                0 ? (
                                                                    availableDevices.map(
                                                                        (
                                                                            device,
                                                                        ) => (
                                                                            <SelectItem
                                                                                key={
                                                                                    device.id
                                                                                }
                                                                                value={device.id.toString()}
                                                                            >
                                                                                {
                                                                                    device.name
                                                                                }
                                                                            </SelectItem>
                                                                        ),
                                                                    )
                                                                ) : (
                                                                    <SelectItem value="no-device">
                                                                        No
                                                                        devices
                                                                        available
                                                                    </SelectItem>
                                                                )}
                                                            </SelectContent>
                                                        </Select>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={control}
                                            name={`configs.${index}.goalCount`}
                                            render={({ field }) => {
                                                // Ensure field.value is a valid number
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : "";
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Goal Count *
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                placeholder="Target production count"
                                                                {...field}
                                                                onChange={(e) =>
                                                                    field.onChange(
                                                                        parseInt(
                                                                            e
                                                                                .target
                                                                                .value,
                                                                        ) || 0,
                                                                    )
                                                                }
                                                                value={value}
                                                            />
                                                        </FormControl>
                                                        <FormDescription>
                                                            Target number of
                                                            parts to produce.
                                                        </FormDescription>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />

                                        <FormField
                                            control={control}
                                            name={`configs.${index}.expectedDuration`}
                                            render={({ field }) => {
                                                // Ensure field.value is a valid number
                                                const value =
                                                    typeof field.value ===
                                                        "number" &&
                                                    !isNaN(field.value) &&
                                                    field.value > 0
                                                        ? field.value.toString()
                                                        : "";
                                                return (
                                                    <FormItem>
                                                        <FormLabel>
                                                            Expected Duration
                                                            (minutes) *
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                placeholder="Expected time in minutes"
                                                                {...field}
                                                                onChange={(e) =>
                                                                    field.onChange(
                                                                        parseInt(
                                                                            e
                                                                                .target
                                                                                .value,
                                                                        ) || 0,
                                                                    )
                                                                }
                                                                value={value}
                                                            />
                                                        </FormControl>
                                                        <FormDescription>
                                                            Estimated completion
                                                            time in minutes.
                                                        </FormDescription>
                                                        <FormMessage />
                                                    </FormItem>
                                                );
                                            }}
                                        />
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </>
                )}
            </CardContent>
            <CardFooter>
                <Button
                    type="button"
                    variant="outline"
                    onClick={addConfiguration}
                    className="w-full"
                >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Device Configuration
                </Button>
            </CardFooter>
        </Card>
    );
};
