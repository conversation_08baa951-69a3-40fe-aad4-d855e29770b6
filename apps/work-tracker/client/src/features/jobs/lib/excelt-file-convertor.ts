import validateExcel from "@/lib/validateExcel";
import ExcelJS from "exceljs";

const expectedHeaders = [
    "Device Key (Required)",
    "JobId (Required)",
    "JobDescription (Required)",
    "PartId (Required)",
    "PartDescription (Required)",
    "GoalCount (In pieces, Must be greater than zero)",
    "IdealCycleTime (Pieces per second, Must be greater than zero)",
    "TaktTime (Pieces per second, Must be greater than Cycle Time)",
    "CountMultiplier1  (Must be greater than zero)",
    "CountMultiplier2  (Must be greater than zero)",
    "CountMultiplier3 (Must be greater than zero)",
    "CountMultiplier4 (Must be greater than zero)",
    "CountMultiplier5 (Must be greater than zero)",
    "CountMultiplier6 (Must be greater than zero)",
    "CountMultiplier7 (Must be greater than zero)",
    "CountMultiplier8 (Must be greater than zero)",
    "TargetMultiplier (Must be greater than zero)",
    "TargetLaborPerPiece (Must be greater than zero)",
    "DownThreshold (In Seconds, Must be greater than zero)",
    "StartWithChangeover (TRUE or FALSE)",
    "ChangeoverTarget (In Seconds, Optional if not starting with changeover, Must be greater than zero)",
    "ChangeoverReason (Optional if not starting with changeover, typically “Part Change”)",
    "DisableWhen (Optional if not starting with changeover, Running and/or Changeover Target)",
];

// Type definitions for Excel row data
interface ExcelRowData {
    deviceKey: string;
    jobId: string;
    jobDescription: string;
    partId: number;
    partDescription: string;
    goalCount: number;
    idealCycleTime: number;
    taktTime: number;
    countMultiplier1: number;
    countMultiplier2: number;
    countMultiplier3: number;
    countMultiplier4: number;
    countMultiplier5: number;
    countMultiplier6: number;
    countMultiplier7: number;
    countMultiplier8: number;
    targetMultiplier: number;
    targetLaborPerPiece: number;
    downThreshold: number;
    startWithChangeover: boolean;
    changeoverTarget?: number;
    changeoverReason?: string;
    disableWhen?: string;
}

// Helper function to parse Excel data
async function parseExcelData(file: File): Promise<ExcelRowData[]> {
    const arrayBuffer = await file.arrayBuffer();
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);

    const worksheet = workbook.worksheets[0];
    if (!worksheet) {
        throw new Error("No sheets found in the Excel file.");
    }

    const data: ExcelRowData[] = [];
    const headerRow = worksheet.getRow(1);

    // Create header mapping
    const headerMap: { [key: string]: number } = {};
    headerRow.eachCell((cell, colNumber) => {
        const header = cell.value?.toString().trim() || "";
        if (header) {
            headerMap[header] = colNumber;
        }
    });

    // Helper function to safely get cell value
    const getCellValue = (row: ExcelJS.Row, headerName: string): any => {
        const colNumber = headerMap[headerName];
        if (colNumber === undefined) {
            return null;
        }
        return row.getCell(colNumber).value;
    };

    // Process data rows
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);

        // Check if row has data by checking if any required fields have values
        const hasRequiredData =
            getCellValue(row, "JobId (Required)") ||
            getCellValue(row, "JobDescription (Required)") ||
            getCellValue(row, "PartId (Required)");

        if (!hasRequiredData) continue;

        const rowData: ExcelRowData = {
            deviceKey:
                getCellValue(row, "Device Key (Required)")?.toString() || "",
            jobId: getCellValue(row, "JobId (Required)")?.toString() || "",
            jobDescription:
                getCellValue(row, "JobDescription (Required)")?.toString() ||
                "",
            partId: Number(getCellValue(row, "PartId (Required)")) || 0,
            partDescription:
                getCellValue(row, "PartDescription (Required)")?.toString() ||
                "",
            goalCount:
                Number(
                    getCellValue(
                        row,
                        "GoalCount (In pieces, Must be greater than zero)",
                    ),
                ) || 0,
            idealCycleTime:
                Number(
                    getCellValue(
                        row,
                        "IdealCycleTime (Pieces per second, Must be greater than zero)",
                    ),
                ) || 0,
            taktTime:
                Number(
                    getCellValue(
                        row,
                        "TaktTime (Pieces per second, Must be greater than Cycle Time)",
                    ),
                ) || 0,
            countMultiplier1:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier1  (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier2:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier2  (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier3:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier3 (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier4:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier4 (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier5:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier5 (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier6:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier6 (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier7:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier7 (Must be greater than zero)",
                    ),
                ) || 0,
            countMultiplier8:
                Number(
                    getCellValue(
                        row,
                        "CountMultiplier8 (Must be greater than zero)",
                    ),
                ) || 0,
            targetMultiplier:
                Number(
                    getCellValue(
                        row,
                        "TargetMultiplier (Must be greater than zero)",
                    ),
                ) || 0,
            targetLaborPerPiece:
                Number(
                    getCellValue(
                        row,
                        "TargetLaborPerPiece (Must be greater than zero)",
                    ),
                ) || 0,
            downThreshold:
                Number(
                    getCellValue(
                        row,
                        "DownThreshold (In Seconds, Must be greater than zero)",
                    ),
                ) || 0,
            startWithChangeover:
                getCellValue(row, "StartWithChangeover (TRUE or FALSE)")
                    ?.toString()
                    .toLowerCase() === "true",
            changeoverTarget: (() => {
                const value = getCellValue(
                    row,
                    "ChangeoverTarget (In Seconds, Optional if not starting with changeover, Must be greater than zero)",
                );
                return value ? Number(value) : undefined;
            })(),
            changeoverReason:
                getCellValue(
                    row,
                    'ChangeoverReason (Optional if not starting with changeover, typically "Part Change")',
                )?.toString() || undefined,
            disableWhen:
                getCellValue(
                    row,
                    "DisableWhen (Optional if not starting with changeover, Running and/or Changeover Target)",
                )?.toString() || undefined,
        };

        data.push(rowData);
    }

    return data;
}

// Main function to insert jobs from Excel using Zero async
export async function insertJobsFromExcel(file: File, z: any) {
    try {
        // Client side validation
        const isValidExcel = await validateExcel(file, expectedHeaders);

        if (!isValidExcel) {
            throw new Error(
                "The file is not a valid excel file. Please check with the template above and try again.",
            );
        }

        // Parse Excel data
        const excelData = await parseExcelData(file);

        if (excelData.length === 0) {
            throw new Error("No data found in the Excel file.");
        }

        // Process each row and create jobs with part configs
        const results = [];
        for (const rowData of excelData) {
            try {
                // Validate required fields
                if (
                    !rowData.jobId ||
                    !rowData.jobDescription ||
                    !rowData.partId
                ) {
                    throw new Error(
                        `Missing required fields in row: JobId, JobDescription, or PartId`,
                    );
                }

                // Check if part exists, create if it doesn't
                let partId = rowData.partId;
                const existingParts = await z.query.parts
                    .where("id", rowData.partId)
                    .run();

                if (existingParts.length === 0) {
                    // Part doesn't exist, create it
                    const allParts = await z.query.parts
                        .orderBy("id", "desc")
                        .limit(1)
                        .run();
                    const latestPartId =
                        allParts.length > 0 ? allParts[0].id : 0;
                    const newPartId = Math.max(
                        latestPartId + 1,
                        rowData.partId,
                    );

                    await z.mutate.parts.insert({
                        id: newPartId,
                        name:
                            rowData.partDescription || `Part ${rowData.partId}`,
                        display_name:
                            rowData.partDescription || `Part ${rowData.partId}`,
                        inserted_date: Date.now(),
                        inserted_by: "Excel Import",
                    });

                    partId = newPartId;
                }

                // Get the latest job ID and increment by 1
                const allJobs = await z.query.jobs
                    .orderBy("id", "desc")
                    .limit(1)
                    .run();
                const latestJobId = allJobs.length > 0 ? allJobs[0].id : 0;
                const newJobId = latestJobId + 1;

                // Create job using Zero mutation
                await z.mutate.jobs.insert({
                    id: newJobId,
                    part_id: partId,
                    name: rowData.jobId,
                    display_name: rowData.jobDescription,
                    rework: false, // Default to false, can be customized
                    is_active: true, // Default to true
                    inserted_date: Date.now(),
                    inserted_by: "Excel Import", // TODO: Get from auth context
                });

                // Create part config if we have device information
                if (rowData.deviceKey) {
                    // Get the latest part config ID and increment by 1
                    const allPartConfigs = await z.query.part_configs
                        .orderBy("id", "desc")
                        .limit(1)
                        .run();
                    const latestPartConfigId =
                        allPartConfigs.length > 0 ? allPartConfigs[0].id : 0;
                    const newPartConfigId = latestPartConfigId + 1;

                    // Find device by name/key (assuming deviceKey maps to device name)
                    const devices = await z.query.devices
                        .where("name", rowData.deviceKey)
                        .run();

                    const deviceId = devices.length > 0 ? devices[0].id : 1; // Default to device 1 if not found

                    await z.mutate.part_configs.insert({
                        id: newPartConfigId,
                        device_type: "default", // TODO: Get from device or make configurable
                        device_id: deviceId,
                        part_id: partId,
                        ideal_cycle_time: rowData.idealCycleTime,
                        takt_time: rowData.taktTime,
                        count_multiplier1: rowData.countMultiplier1,
                        count_multiplier2: rowData.countMultiplier2,
                        count_multiplier3: rowData.countMultiplier3,
                        count_multiplier4: rowData.countMultiplier4,
                        count_multiplier5: rowData.countMultiplier5,
                        count_multiplier6: rowData.countMultiplier6,
                        count_multiplier7: rowData.countMultiplier7,
                        count_multiplier8: rowData.countMultiplier8,
                        target_multiplier: rowData.targetMultiplier,
                        target_labor_per_piece: rowData.targetLaborPerPiece,
                        down_threshold: rowData.downThreshold,
                        start_with_changeover: rowData.startWithChangeover,
                        changeover_target: rowData.changeoverTarget,
                        changeover_reason: rowData.changeoverReason,
                        disable_when: rowData.disableWhen,
                        is_active: true,
                        inserted_date: Date.now(),
                        inserted_by: "Excel Import",
                    });
                }

                results.push({
                    jobId: newJobId,
                    name: rowData.jobId,
                    status: "success",
                });
            } catch (rowError) {
                console.error(
                    `Error processing row ${rowData.jobId}:`,
                    rowError,
                );
                results.push({
                    jobId: rowData.jobId,
                    name: rowData.jobId,
                    status: "error",
                    error:
                        rowError instanceof Error
                            ? rowError.message
                            : "Unknown error",
                });
            }
        }

        // Check if any rows failed
        const failedRows = results.filter((r) => r.status === "error");
        if (failedRows.length > 0) {
            throw new Error(
                `Failed to process ${failedRows.length} rows. Check console for details.`,
            );
        }

        return {
            status: "success",
            message: `Successfully imported ${results.length} jobs`,
            results,
        };
    } catch (err) {
        if (err instanceof Error) {
            return {
                status: "error",
                message: err.message,
            };
        } else {
            return {
                status: "error",
                message:
                    "An error occurred processing the file. Please try again later",
            };
        }
    }
}
