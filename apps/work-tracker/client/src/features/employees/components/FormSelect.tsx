import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface FormSelectProps {
    label: string;
    property: string;
    placeholder: string;
    error: string;
    initialData?: string | number;
    options: { label: string; value: string | number }[];
}

const FormSelect = ({
    label,
    property,
    placeholder,
    error,
    initialData = undefined,
    options,
}: FormSelectProps) => {
    return (
        <>
            <Label htmlFor={property}>{label}</Label>
            <Select
                name={property}
                defaultValue={initialData ? String(initialData) : undefined}
            >
                <SelectTrigger>
                    <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                    {options.map((option) => (
                        <SelectItem
                            key={option.value}
                            value={String(option.value)}
                        >
                            {option.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
            {error && <Label className="text-red-700">{error}</Label>}
        </>
    );
};

export default FormSelect;
