import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Employee } from "@/types";
import clsx from "clsx";
import { MoreHorizontal } from "lucide-react";
import { toast } from "sonner";

interface ActionsCellProps {
    employee: Employee;
    onToggle: (employee: Employee) => void;
    onEditOpen: React.Dispatch<React.SetStateAction<boolean>>
    setSelectedEmployee: React.Dispatch<React.SetStateAction<Employee | undefined>>;
}

export const ActionsCell = ({ employee, onToggle, onEditOpen, setSelectedEmployee }: ActionsCellProps) => {
    const handleCopyEmployeeId = () => {
        navigator.clipboard.writeText(employee.employeeId);
        toast(`Copied Employee ${employee.employeeId} to clipboard`);
    };

    const handleViewDetails = () => {
        setSelectedEmployee(employee)
        onEditOpen(true);
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal />
                    <div className="sr-only">Row Action</div>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuGroup>
                    <DropdownMenuItem onClick={handleCopyEmployeeId}>
                        Copy Employee ID
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleViewDetails}>
                        Edit
                    </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={() => onToggle(employee)}
                    className={clsx(
                        "text-green-500 focus:text-green-500 focus:bg-green-100",
                        employee.isActive &&
                            "text-destructive focus:text-destructive focus:bg-destructive/10",
                    )}
                >
                    {employee.isActive
                        ? "Deactivate Employee"
                        : "Activate Employee"}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default ActionsCell;
