import { Button } from "@/components/ui/button";
import { Column } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";

const HeaderCell = <T,>({
    label,
    className,
    column,
}: {
    label: string;
    className?: string;
    column: Column<T, unknown>;
}) => {
    return (
        <div className={className}>
            <Button
                variant={"ghost"}
                onClick={() =>
                    column.toggleSorting(column.getIsSorted() === "asc")
                }
            >
                {label}
                <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
        </div>
    );
};

export default HeaderCell;
