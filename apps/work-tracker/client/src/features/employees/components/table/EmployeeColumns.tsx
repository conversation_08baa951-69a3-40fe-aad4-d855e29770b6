import { createColumnHelper, <PERSON> } from "@tanstack/react-table";
import ActionsCell from "./ActionsCell";
import { Employee, EmployeeType } from "@/types";
import HeaderCell from "./HeaderCell";

const columnHelper = createColumnHelper<Employee>();

interface EmployeeColumnsProps {
    onToggle: (employee: Employee) => void;
    onEditOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setSelectedEmployee: React.Dispatch<
        React.SetStateAction<Employee | undefined>
    >;
}

const getEmployeeColumns = ({
    onToggle,
    onEditOpen,
    setSelectedEmployee,
}: EmployeeColumnsProps) => [
    columnHelper.accessor("employeeId", {
        header: ({ column }) => (
            <HeaderCell label="ID" column={column} className="" />
        ),
        cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("name", {
        header: ({ column }) => (
            <HeaderCell label="Name" column={column} className="" />
        ),
        cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("type", {
        header: ({ column }) => (
            <HeaderCell label="Type" column={column} className="" />
        ),
        cell: (info) => {
            const typeValue = info.getValue() as number;
            return (
                EmployeeType[typeValue as keyof typeof EmployeeType] ||
                "Unknown"
            );
        },
    }),
    columnHelper.accessor("createdDate", {
        header: ({ column }) => (
            <HeaderCell label="Created On" column={column} className="" />
        ),
        cell: (info) => {
            const dateValue = info.getValue();
            const date = new Date(dateValue);
            return date.toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
            });
        },
    }),
    columnHelper.accessor("isActive", {
        header: ({ column }) => (
            <HeaderCell
                label="Status"
                column={column}
                className="flex justify-center"
            />
        ),
        cell: (info) => (
            <div
                className={`${info.getValue() ? "text-green-600" : "text-red-600"} text-center`}
            >
                {info.getValue() ? "Active" : "Inactive"}
            </div>
        ),
    }),
    columnHelper.accessor("notes", {
        header: ({ column }) => (
            <HeaderCell
                label="Notes"
                column={column}
                className="flex justify-center"
            />
        ),
        cell: (info) => <div className="text-center">{info.getValue()}</div>,
    }),
    columnHelper.display({
        id: "actions",
        header: () => <span className="flex justify-center">Actions</span>,
        cell: ({ row }: { row: Row<Employee> }) => (
            <div className="text-center">
                <ActionsCell
                    employee={row.original}
                    onToggle={onToggle}
                    onEditOpen={onEditOpen}
                    setSelectedEmployee={setSelectedEmployee}
                />
            </div>
        ),
    }),
];

export default getEmployeeColumns;
