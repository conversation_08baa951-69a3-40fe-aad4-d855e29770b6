import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type FormInputProps = {
    label: string;
    property: string;
    type: "text" | "number";
    placeholder?: string;
    initialData?: string;
    error: string;
    readOnly?: boolean;
};

const FormInput = ({
    label,
    property,
    type,
    placeholder,
    initialData,
    error,
    readOnly,
}: FormInputProps) => {
    return (
        <>
            <Label htmlFor={property}>{label}</Label>
            <Input
                id={property}
                name={property}
                type={type}
                placeholder={placeholder}
                defaultValue={initialData ?? ""}
                readOnly={readOnly}
                disabled={readOnly}
            />
            {error && <Label className="text-red-700">{error}</Label>}
        </>
    );
};

export default FormInput;
