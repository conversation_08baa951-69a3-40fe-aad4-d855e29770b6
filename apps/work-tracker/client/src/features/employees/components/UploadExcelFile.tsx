"use client";

import { useCallback, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from "@/components/ui/dialog";
import { Upload } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { bulkImportEmployees } from "../api";

const UploadExcelFile = () => {
    const [file, setFile] = useState<File | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const queryClient = useQueryClient();

    const route = window.location.origin;

    const importEmployeesMutation = useMutation({
        mutationFn: bulkImportEmployees,
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ["employees"] });
        },
    });

    const handleSubmit = useCallback(async () => {
        setError(null);
        if (!file) return;

        const toastId = toast.loading("Importing employees from Excel file...");
        importEmployeesMutation.mutate(file!, {
            onSuccess: () => {
                toast.success(`Successfully imported employees`, {
                    id: toastId,
                });
                setIsOpen(false);
                setFile(null);
            },
            onError: (error) => {
                setError(error.message);
                toast.error(`Failed to import employees`, { id: toastId });
            },
        });
    }, [file, importEmployeesMutation]);

    const handleFile = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setFile(file);
            // Remove any error states
            setError(null);
        }
    };

    const handleOpen = (isOpen: boolean) => {
        setIsOpen(!isOpen);
        setError(null);
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={() => handleOpen(isOpen)}>
                <DialogTrigger asChild>
                    <Button variant={"outline"}>
                        <Upload className="mr-2 size-4" />
                        Import Employees
                    </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Upload Employees from Excel</DialogTitle>
                        <DialogDescription className="text-gray-800">
                            Upload an Excel file to import data into the table.
                            Download a template{" "}
                            <a
                                href={`${route}/api/employees/excel`}
                                target="_blank"
                                className="inline-block hover:underline text-blue-500"
                            >
                                here.
                            </a>
                        </DialogDescription>
                    </DialogHeader>
                    <div
                        className={
                            "rounded border-dashed border-4 flex border-gray-400 relative h-64"
                        }
                    >
                        <input
                            type="file"
                            accept=".xlsx"
                            onChange={handleFile}
                            className="w-full cursor-pointer z-10 bg-gray-100 opacity-0"
                        />
                        <div className="absolute w-full h-full flex justify-center items-center">
                            <h1
                                className={
                                    "text-xl font-semibold text-gray-400"
                                }
                            >
                                {file ? file.name : "Click To Select"}
                            </h1>
                        </div>
                    </div>
                    {error && (
                        <p className=" bg-red-300 text-red-800 text-balance rounded-lg border border-red-500 py-2 px-4">
                            {error}
                        </p>
                    )}
                    <DialogFooter>
                        <Button
                            type="submit"
                            onClick={handleSubmit}
                            disabled={!file}
                        >
                            Import Data
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default UploadExcelFile;
