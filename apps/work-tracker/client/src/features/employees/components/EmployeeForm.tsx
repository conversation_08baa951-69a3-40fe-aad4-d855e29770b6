import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Employee, EmployeeType } from "@/types";
import React, { useState } from "react";
import FormInput from "./FormInput";
import { z } from "zod";
import {
    EmployeeFormSelect,
    EmployeeFormZ as EmployeeFormType,
    employeeSchema,
} from "../types";
import { Loader2 } from "lucide-react";
import FormTextArea from "@/components/forms/FormTextArea";
import FormSelect from "./FormSelect";

interface EmployeeFormProps {
    initialData?: Employee;
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSubmit: (
        data: EmployeeFormType,
    ) => void | boolean | Promise<boolean> | Promise<void>;
    mode: "add" | "edit";
    isLoading?: boolean;
}

const EmployeeForm = ({
    open,
    onOpenChange,
    onSubmit,
    initialData,
    isLoading,
    mode,
}: EmployeeFormProps) => {
    const [errorFields, setErrorFields] = useState<Record<string, string[]>>(
        {},
    );

    const handleError = (error: z.ZodError) => {
        console.error(
            "Employee Zod Validation Error:",
            error.flatten().fieldErrors,
        );
        setErrorFields(error.flatten().fieldErrors as Record<string, string[]>);
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorFields({});

        const formData = new FormData(e.currentTarget);
        const data = {
            employeeId:
                mode == "add"
                    ? String(formData.get("employeeId"))
                    : initialData?.employeeId,
            name: formData.get("name") as string,
            type: Number(formData.get("type")) as keyof typeof EmployeeType,
            notes: formData.get("notes") as string,
        };
        console.log("Submitting employee data:", data);

        try {
            const validatedData = employeeSchema.parse(data);
            console.log(validatedData);
            if (mode == "add") {
                const res = await onSubmit(validatedData);
                if (res) {
                    setErrorFields({
                        employeeId: ["Employee ID already exists."],
                    });
                } else {
                    onOpenChange(false);
                }
            } else {
                onSubmit(validatedData);
                onOpenChange(false);
            }
        } catch (error) {
            handleError(error as z.ZodError);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>
                        {mode === "add" ? "Add New Employee" : "Edit Employee"}
                    </DialogTitle>
                    <DialogDescription className="text-gray-800">
                        Please fill out the details below
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        {/* Employee ID */}
                        <FormInput
                            label={"Employee ID"}
                            property={"employeeId"}
                            type={"number"}
                            error={errorFields?.employeeId?.[0]}
                            placeholder="Enter employee ID"
                            initialData={initialData?.employeeId}
                            readOnly={mode === "edit"}
                        />
                    </div>

                    <div className="space-y-2">
                        {/* Employee Name */}
                        <FormInput
                            label={"Name"}
                            property={"name"}
                            type={"text"}
                            error={errorFields?.name?.[0]}
                            placeholder="Enter employee name"
                            initialData={initialData?.name}
                        />
                    </div>

                    <div className="space-y-2">
                        {/* Employee Type */}
                        <FormSelect
                            label={"Type"}
                            property={"type"}
                            placeholder={"Select employee type"}
                            error={errorFields?.type?.[0]}
                            options={EmployeeFormSelect}
                            initialData={initialData?.type as string | number}
                        />
                    </div>

                    <div className="space-y-2">
                        <FormTextArea
                            property={"notes"}
                            label={"Notes"}
                            placeholder={"Enter any additional notes"}
                            error={errorFields?.notes?.[0]}
                            initialData={initialData?.notes}
                        />
                    </div>
                    <Button type="submit" className="w-full">
                        {isLoading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {mode === "add" ? "Adding..." : "Updating..."}
                            </>
                        ) : mode === "add" ? (
                            "Save Employee"
                        ) : (
                            "Update Employee"
                        )}
                    </Button>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default EmployeeForm;
