import { AlertTriangle } from "lucide-react";
import UploadExcelFile from "@/features/employees/components/UploadExcelFile";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    PaginationState,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import getEmployeeColumns from "./components/table/EmployeeColumns";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import PageLoading from "@/components/loading/PageLoading";
import { useEmployees } from "./hooks/useEmployees";
import EmployeeForm from "./components/EmployeeForm";
import { Employee } from "@/types";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import Pagination from "@/components/table/Pagination";
import type { EmployeeFormZ } from "@/features/employees/types";

const EmployeesTable = () => {
    const {
        data,
        isLoading,
        isError,
        error,
        refetch,
        toggleActivateEmployee,
        addEmployee,
        isAddingEmployee,
        editEmployee,
        isEdittingEmployee,
    } = useEmployees();
    const [addDialogOpen, setAddDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState<
        Employee | undefined
    >();

    const [sorting, setSorting] = useState<SortingState>([]);
    const [globalFilter, setGlobalFilter] = useState("");
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: 0,
        pageSize: 10,
    });

    const columns = useMemo(
        () =>
            getEmployeeColumns({
                onToggle: toggleActivateEmployee,
                onEditOpen: setEditDialogOpen,
                setSelectedEmployee,
            }),
        [toggleActivateEmployee],
    );

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        state: {
            sorting,
            globalFilter,
            pagination,
        },
        onSortingChange: setSorting,
        onGlobalFilterChange: setGlobalFilter,
        onPaginationChange: setPagination,
    });

    if (isLoading) return <PageLoading />;

    if (isError) {
        toast.error(error?.message);
        return (
            <div className="flex flex-col items-center justify-center h-64">
                <AlertTriangle className="w-12 h-12 text-red-500" />
                <p className="text-lg text-gray-700 mt-2">
                    Something went wrong!
                </p>
                <p className="text-sm text-gray-500">{error?.message}</p>
                <button
                    onClick={() => refetch()}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
                >
                    Retry
                </button>
            </div>
        );
    }

    const addEmployeeCheck = async (employee: EmployeeFormZ) => {
        let isOverwrite = false;
        for (const e of data) {
            if (e.employeeId == employee.employeeId) {
                isOverwrite = true;
                break;
            }
        }
        if (isOverwrite) {
            return true;
        } else {
            await addEmployee(employee);
        }
        return false;
    };

    return (
        <div className="w-full space-y-4">
            <div className="flex items-center gap-3">
                <Button onClick={() => setAddDialogOpen(true)}>
                    Add New Employee
                </Button>
                <UploadExcelFile />
                <Input
                    type="text"
                    placeholder="Search employees..."
                    value={globalFilter}
                    onChange={(event) => setGlobalFilter(event.target.value)}
                />
            </div>
            <div className="rounded-md shadow-sm overflow-hidden border border-gray-200">
                <Table className="w-full border-collapse">
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow
                                key={headerGroup.id}
                                className="bg-gray-50"
                            >
                                {headerGroup.headers.map((header) => (
                                    <TableHead
                                        key={header.id}
                                        className="font-semibold py-2"
                                        style={{
                                            width: `${header.getSize()}px`,
                                        }}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext(),
                                              )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={
                                        row.getIsSelected() && "selected"
                                    }
                                    className="border-t border-gray-200"
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext(),
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    No results
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            {/* Pagination */}
            <Pagination table={table} />
            {/* Dialogs */}
            <EmployeeForm
                open={addDialogOpen}
                onOpenChange={setAddDialogOpen}
                onSubmit={addEmployeeCheck}
                mode="add"
                isLoading={isAddingEmployee}
            />
            <EmployeeForm
                open={editDialogOpen}
                onOpenChange={setEditDialogOpen}
                onSubmit={editEmployee}
                mode="edit"
                isLoading={isEdittingEmployee}
                initialData={selectedEmployee}
            />
        </div>
    );
};

export default EmployeesTable;
