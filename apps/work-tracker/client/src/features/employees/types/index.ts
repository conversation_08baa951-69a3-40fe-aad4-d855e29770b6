import { EmployeeType } from "@/types";
import { z } from "zod";

export const employeeSchema = z.object({
    employeeId: z.coerce
        .string()
        .min(1, "Employee ID is required")
        .regex(/^\d+$/, "Employee ID must be numeric"),

    name: z.string().min(1, "Name is required"),
    type: z
        .number({ message: "Please select an employee type" })
        .min(1, { message: "Please select an employee type" })
        .max(2, { message: "Please select an employee type" }),
    notes: z.string().optional(),
});

export type EmployeeFormZ = z.infer<typeof employeeSchema>;

export const EmployeeFormSelect: {
    label: EmployeeType;
    value: keyof typeof EmployeeType;
}[] = [
    {
        label: "Employee",
        value: 1,
    },
    {
        label: "Tech Employee",
        value: 2,
    },
];
