import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    changeEmployeeStatus,
    createEmployee,
    getAllEmployees,
    updateEmployee,
} from "../api";
import { toast } from "sonner";
import { useCallback } from "react";
import { Employee } from "@/types";
import { EmployeeFormZ } from "../types";

export const useEmployees = () => {
    const queryClient = useQueryClient();

    const query = useQuery({
        queryKey: ["employees"],
        queryFn: getAllEmployees,
    });

    const addMutation = useMutation({
        mutationFn: createEmployee,
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ["employees"] });
        },
    });

    const addEmployee = useCallback(
        async (employee: EmployeeFormZ) => {
            console.log(
                `Adding Employee ${employee.employeeId} aka ${employee.name}`,
            );
            addMutation.mutate(employee, {
                onSuccess: () => {
                    toast.success(`Successfully added ${employee.name}`);
                },
                onError: (error: Error) => {
                    console.error(error.message);
                    toast.error(`Failed to add ${employee.name}`);
                },
            });
        },
        [addMutation],
    );

    const toggleActivationMutation = useMutation({
        mutationFn: changeEmployeeStatus,
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ["employees"] });
        },
    });

    const toggleActivateEmployee = useCallback(
        async (employee: Employee) => {
            console.log(
                `Deactivating Employee ${employee.employeeId} aka ${employee.name}`,
            );
            toggleActivationMutation.mutate(
                {
                    id: employee.employeeId,
                    isActive: !employee.isActive,
                },
                {
                    onSuccess: () => {
                        toast.success(
                            `Successfully ${employee.isActive ? "deactivated" : "activated"} ${employee.name}`,
                        );
                    },
                    onError: () => {
                        toast.error(
                            `Failed to ${employee.isActive ? "deactivated" : "activated"} ${employee.name}`,
                        );
                    },
                },
            );
        },
        [toggleActivationMutation],
    );

    const editMutation = useMutation({
        mutationFn: updateEmployee,
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ["employees"] });
        },
    });

    const editEmployee = useCallback(
        async (employee: EmployeeFormZ) => {
            console.log(
                `Editting Employee ${employee.employeeId} aka ${employee.name}`,
            );
            editMutation.mutate(employee, {
                onSuccess: () => {
                    toast.success(`Successfully editted ${employee.name}`);
                },
                onError: (error: Error) => {
                    console.error(error.message);
                    toast.error(`Failed to edit ${employee.name}`);
                },
            });
        },
        [editMutation],
    );

    return {
        data: query.data ?? [],
        isLoading: query.isLoading,
        isError: query.isError,
        error: query.error,
        refetch: query.refetch,
        toggleActivateEmployee,
        addEmployee,
        isAddingEmployee: addMutation.isPending,
        editEmployee,
        isEdittingEmployee: editMutation.isPending,
    };
};
