import validateExcel from "@/lib/validateExcel";
import { Employee } from "@/types";
import { EmployeeFormZ } from "../types";
import { apiUrl } from '@/lib/serverUrl'

const API_URL = new URL("Employees", apiUrl);

export async function getAllEmployees(): Promise<Employee[]> {
    // await new Promise((resolve) => setTimeout(resolve, 5000));
    const response = await fetch(API_URL);
    if (!response.ok) throw new Error("Failed to fetch employees");
    const data: Employee[] = await response.json();
    return data;
}

interface ChangeEmployeeStatusPayload {
    message: string;
}

interface ChangeEmployeeStatus {
    id: string;
    isActive: boolean;
}

export async function changeEmployeeStatus({
    id,
    isActive,
}: ChangeEmployeeStatus): Promise<ChangeEmployeeStatusPayload> {
    const response = await fetch(API_URL + `/activate/${id}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            isActive,
        }),
    });
    if (!response.ok) throw new Error("Failed to change employee status");
    return await response.json();
}

const expectedHeaders = [
    "EmployeeId (Required)",
    "Name (Required)",
    "Type (1 = Employee, 2 = TechEmployee)",
    "Notes (Not required)",
];

export async function bulkImportEmployees(file: File): Promise<void> {
    // Client side validation
    const isValidExcel = await validateExcel(file, expectedHeaders);

    const formData = new FormData();
    formData.append("ExcelFile", file);

    if (!isValidExcel) {
        throw new Error(
            "The file is not a valid excel file. Please check with the template above and try again.",
        );
    }
    const response = await fetch(API_URL + "/excel", {
        method: "POST",
        body: formData,
    });

    if (!response.ok)
        throw new Error("Failed to create new jobs. Please try again");
    // Empty return because no response body is expected
    return;
}

export async function createEmployee(employee: EmployeeFormZ): Promise<void> {
    const response = await fetch(API_URL, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(employee),
    });

    if (!response.ok) throw new Error("Failed to create new employee");
    // Empty return because no response body is expected
    return;
}

export async function updateEmployee(employee: EmployeeFormZ): Promise<void> {
    const response = await fetch(`${API_URL}/${employee.employeeId}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            name: employee.name,
            type: employee.type,
            notes: employee.notes,
        }),
    });

    if (!response.ok) throw new Error("Failed to edit employee");
    // Empty return because no response body is expected
    return;
}
